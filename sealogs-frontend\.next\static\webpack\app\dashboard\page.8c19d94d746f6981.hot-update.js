"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/ui/maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions for generating initials (similar to maintenance list)\nconst getCrewInitials = (assignedTo)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!assignedTo) return \"??\";\n    const names = assignedTo.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Helper function to extract status text using the exact same logic as StatusBadge\n// This ensures consistency between visual display and exported data\nconst getStatusText = (isOverDue)=>{\n    let statusText = \"\";\n    if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(isOverDue.status)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) === \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Upcoming\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) !== \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    }\n    return statusText || \"\";\n};\n// Helper function to create a compatible MaintenanceCheck object for StatusBadge\nconst createMaintenanceCheckForBadge = (reportItem)=>{\n    return {\n        id: 0,\n        assignedTo: {\n            id: 0,\n            name: \"\"\n        },\n        basicComponent: {\n            id: 0,\n            title: null\n        },\n        inventory: {\n            id: 0,\n            item: null\n        },\n        status: reportItem.status || \"\",\n        recurringID: 0,\n        name: reportItem.taskName,\n        created: \"\",\n        severity: \"\",\n        isOverDue: reportItem.dueStatus,\n        comments: null,\n        workOrderNumber: null,\n        startDate: \"\",\n        expires: null,\n        maintenanceCategoryID: 0\n    };\n};\n// Helper function to get status color classes (similar to maintenance list)\nconst getStatusColorClasses = (status)=>{\n    switch(status){\n        case \"High\":\n            return \"text-destructive hover:text-cinnabar-800\";\n        case \"Upcoming\":\n            return \"text-warning hover:text-fire-bush-500\";\n        default:\n            return \"hover:text-curious-blue-400\";\n    }\n};\n// Function to create columns (will be called inside component to access bp and vessel data)\nconst createMaintenanceReportColumns = (bp, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Task Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus;\n                const item = row.original;\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const taskContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"leading-tight truncate font-medium\", getStatusColorClasses(overDueStatus)),\n                    children: item.taskName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 21\n                }, undefined);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"tablet-sm:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        taskContent,\n                                        item.inventoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-curious-blue-400 text-sm\",\n                                                children: item.inventoryName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden tablet-sm:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: taskContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: item.vesselName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-outer-space-400\",\n                                                    children: [\n                                                        \"Location:\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hover:text-curious-blue-400\",\n                                                    children: item.vesselName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.taskName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.taskName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventoryName\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.inventoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hover:text-curious-blue-400\",\n                        children: item.inventoryName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.inventoryName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.inventoryName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Location\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Create a vessel object for VesselIcon (we don't have vessel ID in report data)\n                const vesselForIcon = {\n                    id: 0,\n                    title: item.vesselName\n                };\n                const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                    vessel: vesselWithIcon,\n                    displayText: item.vesselName,\n                    className: \"gap-2.5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"assignedTo\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(item.assignedTo)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hover:text-curious-blue-400 hidden tablet-md:block\",\n                                children: item.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.assignedTo) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.assignedTo) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.status || \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.status) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.status) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"dueDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"dueStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus, _item_dueStatus1;\n                const item = row.original;\n                const maintenanceCheck = createMaintenanceCheckForBadge(item);\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 28\n                    }, undefined);\n                }\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const overDueDays = (_item_dueStatus1 = item.dueStatus) === null || _item_dueStatus1 === void 0 ? void 0 : _item_dueStatus1.days;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n                        children: overDueDays || \"Overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 33\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 33\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_dueStatus, _rowA_original, _rowB_original_dueStatus, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_dueStatus = _rowA_original.dueStatus) === null || _rowA_original_dueStatus === void 0 ? void 0 : _rowA_original_dueStatus.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_dueStatus = _rowB_original.dueStatus) === null || _rowB_original_dueStatus === void 0 ? void 0 : _rowB_original_dueStatus.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n// Row status evaluator for maintenance report (similar to maintenance list)\nconst getMaintenanceReportRowStatus = (reportItem)=>{\n    var _reportItem_dueStatus;\n    // Skip completed, archived, or draft tasks\n    if (reportItem.status === \"Completed\" || reportItem.status === \"Save_As_Draft\") {\n        return \"normal\";\n    }\n    const overDueStatus = (_reportItem_dueStatus = reportItem.dueStatus) === null || _reportItem_dueStatus === void 0 ? void 0 : _reportItem_dueStatus.status;\n    // Use the pre-calculated status values from the system\n    switch(overDueStatus){\n        case \"High\":\n            return \"overdue\" // Red highlighting\n            ;\n        case \"Upcoming\":\n            return \"upcoming\" // Orange highlighting\n            ;\n        case \"Medium\":\n        case \"Open\":\n        default:\n            return \"normal\" // No highlighting\n            ;\n    }\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    // Create columns with access to bp and vessel icon data\n    const columns = createMaintenanceReportColumns(bp, getVesselWithIcon);\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readComponentMaintenanceChecks_nodes, _data_readComponentMaintenanceChecks;\n            // Log unique status values to understand what's available\n            const statusValues = new Set();\n            data === null || data === void 0 ? void 0 : (_data_readComponentMaintenanceChecks = data.readComponentMaintenanceChecks) === null || _data_readComponentMaintenanceChecks === void 0 ? void 0 : (_data_readComponentMaintenanceChecks_nodes = _data_readComponentMaintenanceChecks.nodes) === null || _data_readComponentMaintenanceChecks_nodes === void 0 ? void 0 : _data_readComponentMaintenanceChecks_nodes.forEach((node)=>{\n                if (node.status) {\n                    statusValues.add(node.status);\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"❌ GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error:\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>+item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: +category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: +crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                getStatusText(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                getStatusText(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__.MaintenanceReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 620,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    rowStatus: getMaintenanceReportRowStatus,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 630,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 629,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"awGifJwGcWqIoMGkhaK/iBXUwB4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});