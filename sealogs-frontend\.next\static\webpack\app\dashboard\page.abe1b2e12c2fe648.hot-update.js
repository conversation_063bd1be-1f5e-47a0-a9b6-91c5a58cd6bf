"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../reporting/maintenance-status-activity-report */ \"(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\");\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 171,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 178,\n        columnNumber: 9\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData)();\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                var _maintenanceCheck_name;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_4___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 25\n                        }, this),\n                        !showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \" hover:text-curious-blue-400\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: [\n                                ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-outer-space-400\",\n                                            children: [\n                                                \"Assigned to:\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                            className: \"hover:text-curious-blue-400\",\n                                            children: maintenanceCheck.assignedTo.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                                        maintenanceCheck: maintenanceCheck\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        ...showVessel ? [] : [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 27\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                            vessel: vesselDetails,\n                            vesselId: maintenanceCheck.basicComponent.id,\n                            displayText: maintenanceCheck.basicComponent.title || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 39\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 31\n                    }, this);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ],\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 37\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:text-curious-blue-400 hidden lg:block\",\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:text-curious-blue-400\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 401,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"Zp7KzlBBuyYf0TXkH/rj/yT9nuA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_19__.useBreakpoints)();\n    const [filterIsOverDue, setFilterIsOverDue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (permissions) {\n            init_permissions();\n        }\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_21__.ReadComponentMaintenanceCheckList, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].nodes;\n            if (data) {\n                if (filterIsOverDue) {\n                    const isOverDue = data.filter((task)=>{\n                        return task.isOverDue.day < 0;\n                    });\n                    handleSetMaintenanceChecks(isOverDue);\n                } else {\n                    handleSetMaintenanceChecks(data);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let filter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0,\n                archived: searchParams.get(\"archived\") === \"true\" ? 1 : 0,\n                filter: filter\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_21__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_21__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        // let filteredTasks = maintenanceChecks || []\n        setFilterIsOverDue(false);\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                if (data.value !== \"Overdue\") {\n                    searchFilter.status = {\n                        eq: data.value\n                    };\n                } else {\n                    delete searchFilter.status;\n                    setFilterIsOverDue(true);\n                }\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+(typeof item === \"object\" ? item.value : item))\n                };\n            } else if (data && !Array.isArray(data)) {\n                // Handle both object format {value: id} and direct ID value\n                const memberId = typeof data === \"object\" ? data.value : data;\n                searchFilter.assignedToID = {\n                    eq: +memberId\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(data.startDate).format(\"YYYY-MM-DD 00:00:00\"),\n                    lte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(data.endDate).format(\"YYYY-MM-DD 23:59:59\")\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        // let recurringFilter = null\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                searchFilter.taskType = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.taskType;\n            }\n        }\n        // // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\") {\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_6___default()(data === null || data === void 0 ? void 0 : data.value))) {\n                searchFilter.name = {\n                    contains: data === null || data === void 0 ? void 0 : data.value\n                };\n                keyFilter = data === null || data === void 0 ? void 0 : data.value;\n            } else {\n                delete searchFilter.name;\n                keyFilter = null;\n            }\n        } else if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(keyFilter)) {\n            searchFilter.name = {\n                contains: keyFilter !== null && keyFilter !== void 0 ? keyFilter : \"\"\n            };\n        } else {\n            delete searchFilter.name;\n            keyFilter = null;\n        }\n        // if (type === 'keyword' || (keyFilter && keyFilter.length > 0)) {\n        //     const keyword = data?.value?.trim().toLowerCase()\n        //     if (keyword && keyword.length > 0) {\n        //         filteredTasks = filteredTasks.filter(\n        //             (maintenanceCheck: MaintenanceCheck) =>\n        //                 [\n        //                     maintenanceCheck.name,\n        //                     maintenanceCheck.comments,\n        //                     maintenanceCheck.workOrderNumber,\n        //                 ].some((field) =>\n        //                     field?.toLowerCase().includes(keyword),\n        //                 ),\n        //         )\n        //         keyFilter = data.value\n        //     } else {\n        //         keyFilter = null\n        //     }\n        // }\n        // Filtering based on current searchFilter\n        // // Filter by vessel (basicComponentID)\n        // if (searchFilter.basicComponentID) {\n        //     const ids = searchFilter.basicComponentID.in || [\n        //         searchFilter.basicComponentID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.basicComponent?.id),\n        //     )\n        // }\n        // Filter by status\n        // if (searchFilter.status) {\n        //     const statuses = searchFilter.status.in || [searchFilter.status.eq]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         statuses.includes(mc.status),\n        //     )\n        // }\n        // // Filter by assignedToID\n        // if (searchFilter.assignedToID) {\n        //     const ids = searchFilter.assignedToID.in || [\n        //         searchFilter.assignedToID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.assignedTo?.id),\n        //     )\n        // }\n        // Filter by category\n        // if (searchFilter.maintenanceCategoryID) {\n        //     const ids = searchFilter.maintenanceCategoryID.in || [\n        //         searchFilter.maintenanceCategoryID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.maintenanceCategoryID),\n        //     )\n        // }\n        // // Filter by date range\n        // if (\n        //     searchFilter.expires &&\n        //     searchFilter.expires.gte &&\n        //     searchFilter.expires.lte\n        // ) {\n        //     filteredTasks = filteredTasks.filter(\n        //         (mc: MaintenanceCheck) =>\n        //             dayjs(mc.startDate).isAfter(\n        //                 dayjs(searchFilter.expires!.gte),\n        //             ) &&\n        //             dayjs(mc.startDate).isBefore(\n        //                 dayjs(searchFilter.expires!.lte),\n        //             ),\n        //     )\n        // }\n        // Filter by recurring status\n        // if (recurringFilter) {\n        //     if (recurringFilter === 'recurring') {\n        //         // Recurring tasks have recurringID > 0\n        //         filteredTasks = filteredTasks.filter(\n        //             (mc: MaintenanceCheck) => mc.recurringID > 0,\n        //         )\n        //     } else if (recurringFilter === 'one-off') {\n        //         // One-off tasks have recurringID = 0 or null\n        //         filteredTasks = filteredTasks.filter(\n        //             (mc: MaintenanceCheck) =>\n        //                 !mc.recurringID || mc.recurringID === 0,\n        //         )\n        //     }\n        // }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        // setFilteredMaintenanceChecks(filteredTasks)\n        loadMaintenanceChecks(searchFilter);\n    };\n    const downloadCsv = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task\",\n                \"location\",\n                \"assigned to\",\n                \"due\"\n            ]\n        ];\n        maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").forEach((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            csvEntries.push([\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const headers = [\n            [\n                \"Task Name\",\n                \"Location\",\n                \"Assigned To\",\n                \"Due\"\n            ]\n        ];\n        const body = maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").map((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            return [\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__.exportPdfTable)({\n            headers,\n            body\n        });\n    };\n    const getStatusColorClasses = (status)=>{\n        switch(status){\n            case \"High\":\n                return \"text-destructive hover:text-cinnabar-800\";\n            case \"Upcoming\":\n                return \"text-warning hover:text-fire-bush-500\";\n            default:\n                return \"hover:text-curious-blue-400\";\n        }\n    };\n    const createMaintenanceColumns = (crewInfo, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 868,\n                        columnNumber: 21\n                    }, this);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_basicComponent, _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_assignedTo1, _maintenanceCheck_inventory, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    var _maintenanceCheck_name;\n                    const taskLink = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_23__.cn)(\"leading-tight truncate\", getStatusColorClasses(overDueStatus)),\n                        children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_4___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 885,\n                        columnNumber: 25\n                    }, this);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"tablet-sm:hidden inline-flex overflow-auto items-center gap-1.5\",\n                                children: [\n                                    ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                            className: \"text-xs\",\n                                            children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid\",\n                                        children: [\n                                            taskLink,\n                                            ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:text-curious-blue-400\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 45\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 901,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden tablet-sm:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid items-center gap-2\",\n                                        children: taskLink\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: \"hover:text-curious-blue-400\",\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 937,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 966,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1, _maintenanceCheck_basicComponent2;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    // Get vessel with icon data\n                    const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) || 0, vesselDetails);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                            vessel: vesselWithIcon,\n                            vesselId: maintenanceCheck.basicComponent.id,\n                            displayText: maintenanceCheck.basicComponent.title || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"assigned\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Assigned\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1012,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-lg\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1027,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                    className: \"hover:text-curious-blue-400 hidden tablet-md:block\",\n                                    children: maintenanceCheck.assignedTo.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1035,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1026,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"inventory\",\n                header: \"Inventory\",\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-sm\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_inventory;\n                    const maintenanceCheck = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                            className: \"hover:text-curious-blue-400\",\n                            children: maintenanceCheck.inventory.item\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1064,\n                            columnNumber: 33\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1070,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1079,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"right\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const maintenanceCheck = row.original;\n                    if (!maintenanceCheck) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1086,\n                            columnNumber: 32\n                        }, this);\n                    }\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" items-end w-fit text-sm xs:text-base py-0.5 px-1 xs:px-3 xs:py-1\\n                                                    \".concat(overDueStatus === \"High\" ? \"alert whitespace-nowrap\" : \"\", \"\\n                                                    \"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1096,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1103,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1108,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ]);\n    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon);\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_20__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1161,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1163,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1158,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1168,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_2__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1176,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1166,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"nONp+74DL7ZhxuCYhXSkcUYEX0s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_19__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvbWFpbnRlbmFuY2UvbGlzdC9saXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNrRDtBQUNMO0FBQ1M7QUFDMUI7QUFFSDtBQUNhO0FBQUE7QUFDd0I7QUFDVTtBQUNXO0FBQ2hDO0FBQ0s7QUFLckI7QUFDc0M7QUFDb0I7QUFDckM7QUFDTztBQUNnQjtBQUNmO0FBQ0U7QUFFVjtBQUt0QztBQUNtQztBQUNqQjtBQUVwQyxtQkFBbUI7QUFDbkIsTUFBTWdDLGtCQUFrQixDQUFDQyxXQUFvQkM7UUFFM0JELG1CQUNEQztJQUZiLElBQUksQ0FBQ0QsYUFBYSxDQUFDQyxTQUFTLE9BQU87SUFDbkMsTUFBTUMsUUFBUUYsQ0FBQUEsc0JBQUFBLGlDQUFBQSxvQkFBQUEsVUFBV0csTUFBTSxDQUFDLGdCQUFsQkgsd0NBQUFBLGtCQUFzQkksV0FBVyxPQUFNO0lBQ3JELE1BQU1DLE9BQU9KLENBQUFBLG9CQUFBQSwrQkFBQUEsa0JBQUFBLFFBQVNFLE1BQU0sQ0FBQyxnQkFBaEJGLHNDQUFBQSxnQkFBb0JHLFdBQVcsT0FBTTtJQUNsRCxPQUFPLEdBQVdDLE9BQVJILE9BQWEsT0FBTEcsU0FBVTtBQUNoQztBQUVBLE1BQU1DLG9CQUFvQixDQUFDQztJQUN2QixJQUFJLENBQUNBLE9BQU8sT0FBTztJQUNuQixNQUFNQyxRQUFRRCxNQUFNRSxLQUFLLENBQUMsS0FBS0MsTUFBTSxDQUFDLENBQUNDLE9BQVNBLEtBQUtDLE1BQU0sR0FBRztJQUM5RCxJQUFJSixNQUFNSSxNQUFNLEtBQUssR0FBRztRQUNwQixPQUFPSixLQUFLLENBQUMsRUFBRSxDQUFDSyxTQUFTLENBQUMsR0FBRyxHQUFHVCxXQUFXO0lBQy9DO0lBQ0EsT0FBT0ksTUFDRk0sS0FBSyxDQUFDLEdBQUcsR0FDVEMsR0FBRyxDQUFDLENBQUNKLE9BQVNBLEtBQUtSLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLElBQ3hDWSxJQUFJLENBQUM7QUFDZDtBQUVBLE1BQU1DLGlCQUFpQixDQUFDQyxjQUFzQkM7SUFDMUMsT0FBT0EscUJBQUFBLCtCQUFBQSxTQUFVQyxJQUFJLENBQUMsQ0FBQ0MsT0FBU0EsS0FBS0MsRUFBRSxLQUFLSixhQUFhSyxRQUFRO0FBQ3JFO0FBRU8sTUFBTUMsbUJBQW1CLENBQUNDLFVBQWtCQztJQUMvQyxPQUFPQSxvQkFBQUEsOEJBQUFBLFFBQVNOLElBQUksQ0FBQyxDQUFDTyxTQUFXQSxPQUFPTCxFQUFFLEtBQUtHO0FBQ25ELEVBQUM7QUF1RUQsZ0RBQWdEO0FBQ3pDLE1BQU1HLGNBQWM7UUFBQyxFQUN4QkMsZ0JBQWdCLEVBR25CO1FBQ3FCQSw2QkFLZEEsOEJBS0FBLDhCQUNBQSw4QkFHT0EsOEJBR1BBLDhCQUNRQSw4QkFJUkEsOEJBQ1NBLDhCQUNUQTtJQXhCSixNQUFNQyxZQUFZRCxDQUFBQSw2QkFBQUEsd0NBQUFBLDhCQUFBQSxpQkFBa0JFLFNBQVMsY0FBM0JGLGtEQUFBQSw0QkFBNkJHLE1BQU0sTUFBSztJQUUxRCxrQkFBa0I7SUFDbEIsSUFBSUMsYUFBYTtJQUNqQixJQUNJSixDQUFBQSw2QkFBQUEsd0NBQUFBLCtCQUFBQSxpQkFBa0JFLFNBQVMsY0FBM0JGLG1EQUFBQSw2QkFBNkJHLE1BQU0sS0FDbkM7UUFBQztRQUFRO1FBQVU7S0FBTSxDQUFDRSxRQUFRLENBQUNMLGlCQUFpQkUsU0FBUyxDQUFDQyxNQUFNLEdBQ3RFO1lBQ2VIO1FBQWJJLGFBQWFKLDZCQUFBQSx3Q0FBQUEsZ0NBQUFBLGlCQUFrQkUsU0FBUyxjQUEzQkYsb0RBQUFBLDhCQUE2Qk0sSUFBSTtJQUNsRCxPQUFPLElBQ0hOLENBQUFBLDZCQUFBQSx3Q0FBQUEsK0JBQUFBLGlCQUFrQkUsU0FBUyxjQUEzQkYsbURBQUFBLDZCQUE2QkcsTUFBTSxNQUFLLGVBQ3hDSCxDQUFBQSw2QkFBQUEsd0NBQUFBLCtCQUFBQSxpQkFBa0JFLFNBQVMsY0FBM0JGLG1EQUFBQSw2QkFBNkJNLElBQUksTUFBSyxpQkFDeEM7WUFDZU47UUFBYkksYUFBYUosNkJBQUFBLHdDQUFBQSxnQ0FBQUEsaUJBQWtCRSxTQUFTLGNBQTNCRixvREFBQUEsOEJBQTZCTSxJQUFJO0lBQ2xELE9BQU8sSUFBSU4sQ0FBQUEsNkJBQUFBLHdDQUFBQSwrQkFBQUEsaUJBQWtCRSxTQUFTLGNBQTNCRixtREFBQUEsNkJBQTZCRyxNQUFNLE1BQUssWUFBWTtZQUM5Q0g7UUFBYkksYUFBYUosNkJBQUFBLHdDQUFBQSxnQ0FBQUEsaUJBQWtCRSxTQUFTLGNBQTNCRixvREFBQUEsOEJBQTZCTSxJQUFJO0lBQ2xELE9BQU8sSUFDSE4sQ0FBQUEsNkJBQUFBLHdDQUFBQSwrQkFBQUEsaUJBQWtCRSxTQUFTLGNBQTNCRixtREFBQUEsNkJBQTZCRyxNQUFNLE1BQUssZUFDeEMxRCxxREFBT0EsQ0FBQ3VELDZCQUFBQSx3Q0FBQUEsK0JBQUFBLGlCQUFrQkUsU0FBUyxjQUEzQkYsbURBQUFBLDZCQUE2Qk0sSUFBSSxHQUMzQztZQUNlTjtRQUFiSSxhQUFhSiw2QkFBQUEsd0NBQUFBLGdDQUFBQSxpQkFBa0JFLFNBQVMsY0FBM0JGLG9EQUFBQSw4QkFBNkJHLE1BQU07SUFDcEQsT0FBTyxJQUNISCxDQUFBQSw2QkFBQUEsd0NBQUFBLCtCQUFBQSxpQkFBa0JFLFNBQVMsY0FBM0JGLG1EQUFBQSw2QkFBNkJHLE1BQU0sTUFBSyxlQUN4QyxDQUFDMUQscURBQU9BLENBQUN1RCw2QkFBQUEsd0NBQUFBLCtCQUFBQSxpQkFBa0JFLFNBQVMsY0FBM0JGLG1EQUFBQSw2QkFBNkJNLElBQUksS0FDMUNOLENBQUFBLDZCQUFBQSx3Q0FBQUEsK0JBQUFBLGlCQUFrQkUsU0FBUyxjQUEzQkYsbURBQUFBLDZCQUE2Qk0sSUFBSSxNQUFLLGlCQUN4QztZQUNlTjtRQUFiSSxhQUFhSiw2QkFBQUEsd0NBQUFBLGdDQUFBQSxpQkFBa0JFLFNBQVMsY0FBM0JGLG9EQUFBQSw4QkFBNkJNLElBQUk7SUFDbEQ7SUFFQSw2REFBNkQ7SUFDN0QsSUFBSUwsV0FBVztRQUNYLHFCQUNJLDhEQUFDTTtZQUFLQyxXQUFVO3NCQUNYSjs7Ozs7O0lBR2I7SUFFQSxxQkFDSSw4REFBQ0c7UUFBS0MsV0FBVTtrQkFBb0NKOzs7Ozs7QUFFNUQsRUFBQztLQTlDWUw7QUFnRGIseURBQXlEO0FBQ2xELFNBQVNVLGlCQUFpQixLQVVoQztRQVZnQyxFQUM3QkMsaUJBQWlCLEVBQ2pCYixPQUFPLEVBQ1BQLFFBQVEsRUFDUnFCLGFBQWEsS0FBSyxFQU1yQixHQVZnQzs7SUFXN0IsTUFBTUMsV0FBV2pFLDREQUFXQTtJQUM1QixNQUFNa0UsZUFBZWpFLGdFQUFlQTtJQUNwQyxNQUFNLEVBQUVrRSxjQUFjLEVBQUVDLGlCQUFpQixFQUFFLEdBQUdyRCwrRUFBaUJBO0lBQy9ELE1BQU1zRCxVQUFVN0QseUVBQWFBLENBQUM7UUFDMUI7WUFDSThELGFBQWE7WUFDYkMsUUFBUTtvQkFBQyxFQUFFQyxNQUFNLEVBQW1CO3FDQUNoQyw4REFBQy9ELG9GQUFtQkE7b0JBQUMrRCxRQUFRQTtvQkFBUXpDLE9BQU07Ozs7Ozs7WUFFL0MwQyxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0M7b0JBaUJ2QnJCLGtDQVlKQTtnQkE1QmIsTUFBTUEsbUJBQXFDcUIsSUFBSUMsUUFBUTtvQkFNdEN0QjtnQkFMakIscUJBQ0ksOERBQUN1QjtvQkFBSWYsV0FBVTs7c0NBQ1gsOERBQUNlOzRCQUFJZixXQUFVO3NDQUNYLDRFQUFDakUsaURBQUlBO2dDQUNEaUYsTUFBTSx1QkFBMERaLE9BQW5DWixpQkFBaUJQLEVBQUUsRUFBQyxpQkFBMkJvQixPQUFaRCxVQUFTLEtBQTJCLE9BQXhCQyxhQUFhbkIsUUFBUTswQ0FDaEdNLENBQUFBLHlCQUFBQSxpQkFBaUJ5QixJQUFJLGNBQXJCekIsb0NBQUFBLHlCQUNHLFNBQTRDeEQsT0FBbkN3RCxpQkFBaUJQLEVBQUUsRUFBQyxpQkFFTixPQUZxQmpELDRDQUFLQSxDQUM3Q3dELGlCQUFpQjBCLE9BQU8sRUFDMUJDLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7d0JBS3BCLENBQUNoQiw0QkFDRSw4REFBQ1k7NEJBQUlmLFdBQVU7c0NBQ1ZSLEVBQUFBLG1DQUFBQSxpQkFBaUI0QixjQUFjLGNBQS9CNUIsdURBQUFBLGlDQUFpQ1AsRUFBRSxJQUFHLG1CQUNuQyw4REFBQ2xELGlEQUFJQTtnQ0FDRGlGLE1BQU0sbUJBQXNELE9BQW5DeEIsaUJBQWlCNEIsY0FBYyxDQUFDbkMsRUFBRTtnQ0FDM0RlLFdBQVU7MENBQ1RSLGlCQUFpQjRCLGNBQWMsQ0FBQ2xELEtBQUs7Ozs7Ozs7Ozs7O3NDQU90RCw4REFBQzZDOzRCQUFJZixXQUFVOztnQ0FDVlIsRUFBQUEsK0JBQUFBLGlCQUFpQjZCLFVBQVUsY0FBM0I3QixtREFBQUEsNkJBQTZCUCxFQUFFLElBQUcsbUJBQy9CLDhEQUFDOEI7b0NBQUlmLFdBQVU7O3NEQUNYLDhEQUFDRDs0Q0FBS0MsV0FBVTs7Z0RBQXVCO2dEQUN0Qjs7Ozs7OztzREFFakIsOERBQUNqRSxpREFBSUE7NENBQ0RpRixNQUFNLGlCQUFnRCxPQUEvQnhCLGlCQUFpQjZCLFVBQVUsQ0FBQ3BDLEVBQUU7NENBQ3JEZSxXQUFVO3NEQUNUUixpQkFBaUI2QixVQUFVLENBQUNKLElBQUk7Ozs7Ozs7Ozs7Ozs4Q0FJN0MsOERBQUNGOzhDQUNHLDRFQUFDeEI7d0NBQ0dDLGtCQUFrQkE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTTFDO1lBQ0E4QixXQUFXLENBQ1BDLE1BQ0FDO29CQUVlRCxnQkFDQUM7Z0JBRGYsTUFBTUMsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVQsUUFBUSxjQUFkUyxxQ0FBQUEsZUFBZ0JOLElBQUksS0FBSTtnQkFDdkMsTUFBTVMsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVYsUUFBUSxjQUFkVSxxQ0FBQUEsZUFBZ0JQLElBQUksS0FBSTtnQkFDdkMsT0FBT1EsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO1dBQ0l2QixhQUNFLEVBQUUsR0FDRjtZQUNJO2dCQUNJTSxhQUFhO2dCQUNiQyxRQUFRO3dCQUFDLEVBQUVDLE1BQU0sRUFBbUI7eUNBQ2hDLDhEQUFDL0Qsb0ZBQW1CQTt3QkFDaEIrRCxRQUFRQTt3QkFDUnpDLE9BQU07Ozs7Ozs7Z0JBR2QwRCxlQUFlO2dCQUNmaEIsTUFBTTt3QkFBQyxFQUFFQyxHQUFHLEVBQWdDO3dCQUdwQ3JCLGtDQU1LQTtvQkFSVCxNQUFNQSxtQkFBbUJxQixJQUFJQyxRQUFRO29CQUNyQyxNQUFNZSxnQkFBZ0IxQyxpQkFDbEJLLEVBQUFBLG1DQUFBQSxpQkFBaUI0QixjQUFjLGNBQS9CNUIsdURBQUFBLGlDQUFpQ1AsRUFBRSxLQUFJLEdBQ3ZDSTtvQkFHSixxQkFDSSw4REFBQzBCO3dCQUFJZixXQUFVO2tDQUNWUixFQUFBQSxvQ0FBQUEsaUJBQWlCNEIsY0FBYyxjQUEvQjVCLHdEQUFBQSxrQ0FBaUNQLEVBQUUsSUFBRyxtQkFDbkMsOERBQUNoQywwRkFBcUJBOzRCQUNsQnFDLFFBQVF1Qzs0QkFDUkMsVUFDSXRDLGlCQUFpQjRCLGNBQWMsQ0FBQ25DLEVBQUU7NEJBRXRDOEMsYUFDSXZDLGlCQUFpQjRCLGNBQWMsQ0FDMUJsRCxLQUFLLElBQUk7Ozs7Ozs7Ozs7O2dCQU10QztnQkFDQW9ELFdBQVcsQ0FDUEMsTUFDQUM7d0JBR0lELCtCQUFBQSxnQkFFQUMsK0JBQUFBO29CQUhKLE1BQU1DLFNBQ0ZGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ULFFBQVEsY0FBZFMsc0NBQUFBLGdDQUFBQSxlQUFnQkgsY0FBYyxjQUE5Qkcsb0RBQUFBLDhCQUFnQ3JELEtBQUssS0FBSTtvQkFDN0MsTUFBTXdELFNBQ0ZGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1WLFFBQVEsY0FBZFUsc0NBQUFBLGdDQUFBQSxlQUFnQkosY0FBYyxjQUE5Qkksb0RBQUFBLDhCQUFnQ3RELEtBQUssS0FBSTtvQkFDN0MsT0FBT3VELE9BQU9FLGFBQWEsQ0FBQ0Q7Z0JBQ2hDO1lBQ0o7U0FDSDtRQUNQO1lBQ0lqQixhQUFhO1lBQ2JDLFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUMvRCxvRkFBbUJBO29CQUFDK0QsUUFBUUE7b0JBQVF6QyxPQUFNOzs7Ozs7O1lBRS9DMEQsZUFBZTtZQUNmSSxZQUFZO1lBQ1pwQixNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0M7b0JBR3BDckIsOEJBTUtBO2dCQVJULE1BQU1BLG1CQUFtQnFCLElBQUlDLFFBQVE7Z0JBQ3JDLE1BQU1tQixjQUFjckQsZUFDaEJZLEVBQUFBLCtCQUFBQSxpQkFBaUI2QixVQUFVLGNBQTNCN0IsbURBQUFBLDZCQUE2QlAsRUFBRSxLQUFJLEdBQ25DSDtnQkFHSixxQkFDSSw4REFBQ2lDO29CQUFJZixXQUFVOzhCQUNWUixFQUFBQSxnQ0FBQUEsaUJBQWlCNkIsVUFBVSxjQUEzQjdCLG9EQUFBQSw4QkFBNkJQLEVBQUUsSUFBRyxtQkFDL0IsOERBQUM4Qjt3QkFBSWYsV0FBVTs7MENBQ1gsOERBQUNqRCwwREFBTUE7Z0NBQUNpRCxXQUFVOzBDQUNkLDRFQUFDaEQsa0VBQWNBO29DQUFDZ0QsV0FBVTs4Q0FDckJ0QyxnQkFDR3VFLHdCQUFBQSxrQ0FBQUEsWUFBYXRFLFNBQVMsRUFDdEJzRSx3QkFBQUEsa0NBQUFBLFlBQWFyRSxPQUFPOzs7Ozs7Ozs7OzswQ0FJaEMsOERBQUM3QixpREFBSUE7Z0NBQ0RpRixNQUFNLGlCQUFnRCxPQUEvQnhCLGlCQUFpQjZCLFVBQVUsQ0FBQ3BDLEVBQUU7Z0NBQ3JEZSxXQUFVOzBDQUNUUixpQkFBaUI2QixVQUFVLENBQUNKLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTXpEO1lBQ0FLLFdBQVcsQ0FDUEMsTUFDQUM7b0JBRWVELDJCQUFBQSxnQkFDQUMsMkJBQUFBO2dCQURmLE1BQU1DLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ULFFBQVEsY0FBZFMsc0NBQUFBLDRCQUFBQSxlQUFnQkYsVUFBVSxjQUExQkUsZ0RBQUFBLDBCQUE0Qk4sSUFBSSxLQUFJO2dCQUNuRCxNQUFNUyxTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNVixRQUFRLGNBQWRVLHNDQUFBQSw0QkFBQUEsZUFBZ0JILFVBQVUsY0FBMUJHLGdEQUFBQSwwQkFBNEJQLElBQUksS0FBSTtnQkFDbkQsT0FBT1EsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO1FBQ0E7WUFDSWpCLGFBQWE7WUFDYkMsUUFBUTtZQUNSa0IsZUFBZTtZQUNmSSxZQUFZO1lBQ1pwQixNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0M7b0JBSS9CckI7Z0JBSFQsTUFBTUEsbUJBQW1CcUIsSUFBSUMsUUFBUTtnQkFDckMscUJBQ0ksOERBQUNDO29CQUFJZixXQUFVOzhCQUNWUixFQUFBQSw4QkFBQUEsaUJBQWlCMEMsU0FBUyxjQUExQjFDLGtEQUFBQSw0QkFBNEJQLEVBQUUsSUFBRyxtQkFDOUIsOERBQUNsRCxpREFBSUE7d0JBQ0RpRixNQUFNLHNCQUFvRCxPQUE5QnhCLGlCQUFpQjBDLFNBQVMsQ0FBQ2pELEVBQUU7d0JBQ3pEZSxXQUFVO2tDQUNUUixpQkFBaUIwQyxTQUFTLENBQUNDLElBQUk7Ozs7Ozs7Ozs7O1lBS3BEO1FBQ0o7UUFDQTtZQUNJMUIsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDL0Qsb0ZBQW1CQTtvQkFBQytELFFBQVFBO29CQUFRekMsT0FBTTs7Ozs7OztZQUUvQzBELGVBQWU7WUFDZmhCLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQztnQkFDeEMsTUFBTXJCLG1CQUFtQnFCLElBQUlDLFFBQVE7Z0JBQ3JDLHFCQUNJLDhEQUFDQztvQkFBSWYsV0FBVTs4QkFDWCw0RUFBQ1Q7d0JBQVlDLGtCQUFrQkE7Ozs7Ozs7Ozs7O1lBRzNDO1lBQ0E4QixXQUFXLENBQ1BDLE1BQ0FDO29CQUVlRCwwQkFBQUEsZ0JBQ0FDLDBCQUFBQTtnQkFEZixNQUFNQyxTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNVCxRQUFRLGNBQWRTLHNDQUFBQSwyQkFBQUEsZUFBZ0I3QixTQUFTLGNBQXpCNkIsK0NBQUFBLHlCQUEyQnpCLElBQUksS0FBSTtnQkFDbEQsTUFBTTRCLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1WLFFBQVEsY0FBZFUsc0NBQUFBLDJCQUFBQSxlQUFnQjlCLFNBQVMsY0FBekI4QiwrQ0FBQUEseUJBQTJCMUIsSUFBSSxLQUFJO2dCQUNsRCxPQUFPMkIsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO0tBQ0g7SUFFRCxxQkFDSSw4REFBQ2hGLGlFQUFTQTtRQUNOOEQsU0FBU0E7UUFDVDRCLE1BQU1sQztRQUNObUMsVUFBVTtRQUNWQyxhQUFhOzs7Ozs7QUFHekI7R0FqT2dCckM7O1FBV0s5RCx3REFBV0E7UUFDUEMsNERBQWVBO1FBQ1VjLDJFQUFpQkE7OztNQWJuRCtDO0FBbU9ELFNBQVNzQzs7O0lBQ3BCLE1BQU0sQ0FBQ3JDLG1CQUFtQnNDLHFCQUFxQixHQUMzQzVHLCtDQUFRQTtJQUNaLE1BQU0sQ0FBQzZHLDJCQUEyQkMsNkJBQTZCLEdBQzNEOUcsK0NBQVFBO0lBQ1osTUFBTSxDQUFDeUQsU0FBU3NELFdBQVcsR0FBRy9HLCtDQUFRQTtJQUN0QyxNQUFNLENBQUNrRCxVQUFVOEQsWUFBWSxHQUFHaEgsK0NBQVFBO0lBQ3hDLE1BQU0sQ0FBQ3lDLFFBQVF3RSxVQUFVLEdBQUdqSCwrQ0FBUUEsQ0FBQyxDQUFDO0lBQ3RDLE1BQU0sQ0FBQ2tILFdBQVdDLGFBQWEsR0FBR25ILCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ29ILGVBQWVDLGlCQUFpQixHQUFHckgsK0NBQVFBLENBQWdCO0lBQ2xFLE1BQU0sQ0FBQ3NILGFBQWFDLGVBQWUsR0FBR3ZILCtDQUFRQSxDQUFNO0lBQ3BELE1BQU0sQ0FBQ3dILFdBQVdDLGFBQWEsR0FBR3pILCtDQUFRQSxDQUFVO0lBQ3BELE1BQU13RSxXQUFXakUsNERBQVdBO0lBQzVCLE1BQU1rRSxlQUFlakUsZ0VBQWVBO0lBQ3BDLE1BQU0sRUFBRW1FLGlCQUFpQixFQUFFLEdBQUdyRCwrRUFBaUJBO0lBQy9DLE1BQU1vRyxLQUFLbkcsaUZBQWNBO0lBQ3pCLE1BQU0sQ0FBQ29HLGlCQUFpQkMsbUJBQW1CLEdBQUc1SCwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNNkgsbUJBQW1CO1FBQ3JCLElBQUlQLGFBQWE7WUFDYixJQUFJNUcsc0VBQWFBLENBQUMsYUFBYTRHLGNBQWM7Z0JBQ3pDRyxhQUFhO1lBQ2pCLE9BQU87Z0JBQ0hBLGFBQWE7WUFDakI7UUFDSjtJQUNKO0lBRUExSCxnREFBU0EsQ0FBQztRQUNOd0gsZUFBZTlHLG1FQUFjQTtRQUM3Qm9IO0lBQ0osR0FBRyxFQUFFO0lBRUw5SCxnREFBU0EsQ0FBQztRQUNOLElBQUl1SCxhQUFhO1lBQ2JPO1FBQ0o7SUFDSixHQUFHO1FBQUNQO0tBQVk7SUFFaEIsTUFBTSxDQUFDUSx1QkFBdUIsR0FBRzdILDZEQUFZQSxDQUN6Q3dCLHdFQUFpQ0EsRUFDakM7UUFDSXNHLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU16QixPQUFPeUIsU0FBU0MsaUNBQWlDLENBQUMsRUFBRSxDQUFDQyxLQUFLO1lBQ2hFLElBQUkzQixNQUFNO2dCQUNOLElBQUltQixpQkFBaUI7b0JBQ2pCLE1BQU03RCxZQUFZMEMsS0FBSy9ELE1BQU0sQ0FBQyxDQUFDMkY7d0JBQzNCLE9BQU9BLEtBQUt0RSxTQUFTLENBQUN1RSxHQUFHLEdBQUc7b0JBQ2hDO29CQUNBQywyQkFBMkJ4RTtnQkFDL0IsT0FBTztvQkFDSHdFLDJCQUEyQjlCO2dCQUMvQjtZQUNKO1FBQ0o7UUFDQStCLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDbEQ7SUFDSjtJQUVKekksZ0RBQVNBLENBQUM7UUFDTixJQUFJbUgsV0FBVztZQUNYd0I7WUFDQXZCLGFBQWE7UUFDakI7SUFDSixHQUFHO1FBQUNEO0tBQVU7SUFDZCxNQUFNd0Isd0JBQXdCO1lBQU9qRywwRUFBYyxDQUFDO1FBQ2hELE1BQU1xRix1QkFBdUI7WUFDekJhLFdBQVc7Z0JBQ1BDLGFBQWE7Z0JBQ2JwRixVQUFVO2dCQUNWcUYsVUFBVXBFLGFBQWFxRSxHQUFHLENBQUMsZ0JBQWdCLFNBQVMsSUFBSTtnQkFDeERyRyxRQUFRQTtZQUNaO1FBQ0o7SUFDSjtJQUNBLE1BQU1zRyxtQkFBbUIsQ0FBQ3RGO1FBQ3RCLE1BQU11RixnQkFBZ0J2RixRQUFRaEIsTUFBTSxDQUFDLENBQUNpQixTQUFnQixDQUFDQSxPQUFPbUYsUUFBUTtRQUN0RSxNQUFNSSxlQUFlRCxjQUFjbEcsR0FBRyxDQUFDLENBQUN5RCxPQUFlO2dCQUNuRCxHQUFHQSxJQUFJO1lBQ1g7UUFDQTBDLGFBQWFDLElBQUksQ0FBQztZQUFFNUcsT0FBTztZQUFTZSxJQUFJO1FBQUU7UUFDMUMwRCxXQUFXa0M7SUFDZjtJQUVBLE1BQU1FLGdCQUFnQixTQUFDSjtZQUF1QkssMkVBQW1COztRQUM3RCxNQUFNLENBQUNsQyxXQUFXQyxhQUFhLEdBQUduSCwrQ0FBUUEsQ0FBQztRQUMzQyxNQUFNcUosY0FBYyxJQUFJekgsbUVBQVdBO1FBQ25DN0IsZ0RBQVNBLENBQUM7WUFDTixJQUFJbUgsV0FBVztnQkFDWG9DO2dCQUNBbkMsYUFBYTtZQUNqQjtRQUNKLEdBQUc7WUFBQ0Q7U0FBVTtRQUVkLE1BQU0sQ0FBQ3FDLGFBQWEsR0FBR3RKLDZEQUFZQSxDQUFDMEIsa0RBQVdBLEVBQUU7WUFDN0NvRyxhQUFhO1lBQ2JDLGFBQWEsQ0FBQ3dCO2dCQUNWLElBQUlBLG9CQUFvQkMsV0FBVyxDQUFDdEIsS0FBSyxFQUFFO29CQUN2Q1ksaUJBQWlCUyxvQkFBb0JDLFdBQVcsQ0FBQ3RCLEtBQUs7Z0JBQzFEO1lBQ0o7WUFDQUksU0FBUyxDQUFDQztnQkFDTkMsUUFBUUQsS0FBSyxDQUFDLHNCQUFzQkE7WUFDeEM7UUFDSjtRQUNBLE1BQU1jLGNBQWM7WUFDaEIsSUFBSUYsU0FBUztnQkFDVCxNQUFNbkIsV0FBVyxNQUFNb0IsWUFBWUssTUFBTTtnQkFDekNYLGlCQUFpQmQ7WUFDckIsT0FBTztnQkFDSCxNQUFNc0IsYUFBYTtvQkFDZlosV0FBVzt3QkFDUGdCLE9BQU87d0JBQ1BDLFFBQVE7b0JBQ1o7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7T0FsQ01UOztZQVVxQmxKLHlEQUFZQTs7O0lBeUJ2Q2tKLGNBQWNKO0lBRWQsTUFBTVQsNkJBQTZCLENBQUN1QjtRQUNoQ2pELHFCQUFxQmlEO1FBQ3JCL0MsNkJBQTZCK0M7UUFDN0IsTUFBTVosZUFBeUJhLE1BQU1DLElBQUksQ0FDckMsSUFBSUMsSUFDQUgsTUFDS3BILE1BQU0sQ0FBQyxDQUFDOEQsT0FBY0EsS0FBS2QsVUFBVSxDQUFDcEMsRUFBRSxHQUFHLEdBQzNDUCxHQUFHLENBQUMsQ0FBQ3lELE9BQWNBLEtBQUtkLFVBQVUsQ0FBQ3BDLEVBQUU7UUFHbEQ0RyxtQkFBbUJoQjtJQUN2QjtJQUVBLE1BQU0sQ0FBQ2lCLG9CQUFvQixHQUFHakssNkRBQVlBLENBQUN5Qix5REFBa0JBLEVBQUU7UUFDM0RxRyxhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVixNQUFNekIsT0FBT3lCLFNBQVNrQyxrQkFBa0IsQ0FBQ2hDLEtBQUs7WUFDOUMsSUFBSTNCLE1BQU07Z0JBQ05RLFlBQVlSO1lBQ2hCO1FBQ0o7UUFDQStCLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7UUFDL0M7SUFDSjtJQUNBLE1BQU15QixxQkFBcUIsT0FBT0c7UUFDOUIsTUFBTUYsb0JBQW9CO1lBQ3RCdkIsV0FBVztnQkFDUDBCLGVBQWVELE9BQU96SCxNQUFNLEdBQUcsSUFBSXlILFNBQVM7b0JBQUM7aUJBQUU7WUFDbkQ7UUFDSjtJQUNKO0lBQ0EsTUFBTUUsdUJBQXVCO1lBQUMsRUFBRUMsSUFBSSxFQUFFL0QsSUFBSSxFQUFPO1FBQzdDLE1BQU1nRSxlQUE2QjtZQUFFLEdBQUcvSCxNQUFNO1FBQUM7UUFDL0MsOENBQThDO1FBQzlDbUYsbUJBQW1CO1FBQ25CLGdCQUFnQjtRQUNoQixJQUFJMkMsU0FBUyxVQUFVO1lBQ25CLElBQUlULE1BQU1XLE9BQU8sQ0FBQ2pFLFNBQVNBLEtBQUs3RCxNQUFNLEdBQUcsR0FBRztnQkFDeEM2SCxhQUFhRSxnQkFBZ0IsR0FBRztvQkFDNUJDLElBQUluRSxLQUFLMUQsR0FBRyxDQUFDLENBQUN5RCxPQUFTLENBQUNBLEtBQUtxRSxLQUFLO2dCQUN0QztZQUNKLE9BQU8sSUFBSXBFLFFBQVEsQ0FBQ3NELE1BQU1XLE9BQU8sQ0FBQ2pFLE9BQU87Z0JBQ3JDZ0UsYUFBYUUsZ0JBQWdCLEdBQUc7b0JBQUVHLElBQUksQ0FBQ3JFLEtBQUtvRSxLQUFLO2dCQUFDO1lBQ3RELE9BQU87Z0JBQ0gsT0FBT0osYUFBYUUsZ0JBQWdCO1lBQ3hDO1FBQ0o7UUFFQSxnQkFBZ0I7UUFDaEIsSUFBSUgsU0FBUyxVQUFVO1lBQ25CLElBQUlULE1BQU1XLE9BQU8sQ0FBQ2pFLFNBQVNBLEtBQUs3RCxNQUFNLEdBQUcsR0FBRztnQkFDeEM2SCxhQUFhekcsTUFBTSxHQUFHO29CQUFFNEcsSUFBSW5FLEtBQUsxRCxHQUFHLENBQUMsQ0FBQ3lELE9BQVNBLEtBQUtxRSxLQUFLO2dCQUFFO1lBQy9ELE9BQU8sSUFBSXBFLFFBQVEsQ0FBQ3NELE1BQU1XLE9BQU8sQ0FBQ2pFLE9BQU87Z0JBQ3JDLElBQUlBLEtBQUtvRSxLQUFLLEtBQUssV0FBVztvQkFDMUJKLGFBQWF6RyxNQUFNLEdBQUc7d0JBQUU4RyxJQUFJckUsS0FBS29FLEtBQUs7b0JBQUM7Z0JBQzNDLE9BQU87b0JBQ0gsT0FBT0osYUFBYXpHLE1BQU07b0JBQzFCNkQsbUJBQW1CO2dCQUN2QjtZQUNKLE9BQU87Z0JBQ0gsT0FBTzRDLGFBQWF6RyxNQUFNO1lBQzlCO1FBQ0o7UUFFQSx5QkFBeUI7UUFDekIsSUFBSXdHLFNBQVMsVUFBVTtZQUNuQixJQUFJVCxNQUFNVyxPQUFPLENBQUNqRSxTQUFTQSxLQUFLN0QsTUFBTSxHQUFHLEdBQUc7Z0JBQ3hDNkgsYUFBYXZILFlBQVksR0FBRztvQkFDeEIwSCxJQUFJbkUsS0FBSzFELEdBQUcsQ0FDUixDQUFDeUQsT0FDRyxDQUFFLFFBQU9BLFNBQVMsV0FBV0EsS0FBS3FFLEtBQUssR0FBR3JFLElBQUc7Z0JBRXpEO1lBQ0osT0FBTyxJQUFJQyxRQUFRLENBQUNzRCxNQUFNVyxPQUFPLENBQUNqRSxPQUFPO2dCQUNyQyw0REFBNEQ7Z0JBQzVELE1BQU1zRSxXQUFXLE9BQU90RSxTQUFTLFdBQVdBLEtBQUtvRSxLQUFLLEdBQUdwRTtnQkFDekRnRSxhQUFhdkgsWUFBWSxHQUFHO29CQUFFNEgsSUFBSSxDQUFDQztnQkFBUztZQUNoRCxPQUFPO2dCQUNILE9BQU9OLGFBQWF2SCxZQUFZO1lBQ3BDO1FBQ0o7UUFFQSxhQUFhO1FBQ2IsSUFBSXNILFNBQVMsYUFBYTtZQUN0QixJQUFJL0QsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNdUUsU0FBUyxNQUFJdkUsaUJBQUFBLDJCQUFBQSxLQUFNd0UsT0FBTyxHQUFFO2dCQUNsQ1IsYUFBYVMsT0FBTyxHQUFHO29CQUNuQkMsS0FBSzlLLDRDQUFLQSxDQUFDb0csS0FBS3VFLFNBQVMsRUFBRXhGLE1BQU0sQ0FBQztvQkFDbEM0RixLQUFLL0ssNENBQUtBLENBQUNvRyxLQUFLd0UsT0FBTyxFQUFFekYsTUFBTSxDQUFDO2dCQUNwQztZQUNKLE9BQU87Z0JBQ0gsT0FBT2lGLGFBQWFTLE9BQU87WUFDL0I7UUFDSjtRQUVBLFdBQVc7UUFDWCxJQUFJVixTQUFTLFlBQVk7WUFDckIsSUFBSVQsTUFBTVcsT0FBTyxDQUFDakUsU0FBU0EsS0FBSzdELE1BQU0sR0FBRyxHQUFHO2dCQUN4QzZILGFBQWFZLHFCQUFxQixHQUFHO29CQUNqQ1QsSUFBSW5FLEtBQUsxRCxHQUFHLENBQUMsQ0FBQ3lELE9BQVMsQ0FBQ0EsS0FBS3FFLEtBQUs7Z0JBQ3RDO1lBQ0osT0FBTyxJQUFJcEUsUUFBUSxDQUFDc0QsTUFBTVcsT0FBTyxDQUFDakUsT0FBTztnQkFDckNnRSxhQUFhWSxxQkFBcUIsR0FBRztvQkFBRVAsSUFBSSxDQUFDckUsS0FBS29FLEtBQUs7Z0JBQUM7WUFDM0QsT0FBTztnQkFDSCxPQUFPSixhQUFhWSxxQkFBcUI7WUFDN0M7UUFDSjtRQUVBLGtEQUFrRDtRQUNsRCw2QkFBNkI7UUFFN0IsSUFBSWIsU0FBUyxhQUFhO1lBQ3RCLElBQUkvRCxRQUFRLENBQUNzRCxNQUFNVyxPQUFPLENBQUNqRSxPQUFPO2dCQUM5QmdFLGFBQWFhLFFBQVEsR0FBRztvQkFBRVIsSUFBSXJFLEtBQUtvRSxLQUFLO2dCQUFDO1lBQzdDLE9BQU87Z0JBQ0gsT0FBT0osYUFBYWEsUUFBUTtZQUNoQztRQUNKO1FBRUEsb0JBQW9CO1FBQ3BCLElBQUlDLFlBQVlsRTtRQUNoQixJQUFJbUQsU0FBUyxXQUFXO1lBQ3BCLElBQUksQ0FBQ2xLLHFEQUFPQSxDQUFDQyxrREFBSUEsQ0FBQ2tHLGlCQUFBQSwyQkFBQUEsS0FBTW9FLEtBQUssSUFBSTtnQkFDN0JKLGFBQWFuRixJQUFJLEdBQUc7b0JBQUVrRyxRQUFRLEVBQUUvRSxpQkFBQUEsMkJBQUFBLEtBQU1vRSxLQUFLO2dCQUFDO2dCQUM1Q1UsWUFBWTlFLGlCQUFBQSwyQkFBQUEsS0FBTW9FLEtBQUs7WUFDM0IsT0FBTztnQkFDSCxPQUFPSixhQUFhbkYsSUFBSTtnQkFDeEJpRyxZQUFZO1lBQ2hCO1FBQ0osT0FBTyxJQUFJLENBQUNqTCxxREFBT0EsQ0FBQ2lMLFlBQVk7WUFDNUJkLGFBQWFuRixJQUFJLEdBQUc7Z0JBQUVrRyxVQUFVRCxzQkFBQUEsdUJBQUFBLFlBQWE7WUFBRztRQUNwRCxPQUFPO1lBQ0gsT0FBT2QsYUFBYW5GLElBQUk7WUFDeEJpRyxZQUFZO1FBQ2hCO1FBQ0EsbUVBQW1FO1FBQ25FLHdEQUF3RDtRQUN4RCwyQ0FBMkM7UUFDM0MsZ0RBQWdEO1FBQ2hELHNEQUFzRDtRQUN0RCxvQkFBb0I7UUFDcEIsNkNBQTZDO1FBQzdDLGlEQUFpRDtRQUNqRCx3REFBd0Q7UUFDeEQsb0NBQW9DO1FBQ3BDLDhEQUE4RDtRQUM5RCxxQkFBcUI7UUFDckIsWUFBWTtRQUNaLGlDQUFpQztRQUNqQyxlQUFlO1FBQ2YsMkJBQTJCO1FBQzNCLFFBQVE7UUFDUixJQUFJO1FBRUosMENBQTBDO1FBRTFDLHlDQUF5QztRQUN6Qyx1Q0FBdUM7UUFDdkMsd0RBQXdEO1FBQ3hELDRDQUE0QztRQUM1QyxRQUFRO1FBQ1IscUVBQXFFO1FBQ3JFLCtDQUErQztRQUMvQyxRQUFRO1FBQ1IsSUFBSTtRQUVKLG1CQUFtQjtRQUNuQiw2QkFBNkI7UUFDN0IsMEVBQTBFO1FBQzFFLHFFQUFxRTtRQUNyRSx3Q0FBd0M7UUFDeEMsUUFBUTtRQUNSLElBQUk7UUFFSiw0QkFBNEI7UUFDNUIsbUNBQW1DO1FBQ25DLG9EQUFvRDtRQUNwRCx3Q0FBd0M7UUFDeEMsUUFBUTtRQUNSLHFFQUFxRTtRQUNyRSwyQ0FBMkM7UUFDM0MsUUFBUTtRQUNSLElBQUk7UUFFSixxQkFBcUI7UUFDckIsNENBQTRDO1FBQzVDLDZEQUE2RDtRQUM3RCxpREFBaUQ7UUFDakQsUUFBUTtRQUNSLHFFQUFxRTtRQUNyRSxrREFBa0Q7UUFDbEQsUUFBUTtRQUNSLElBQUk7UUFFSiwwQkFBMEI7UUFDMUIsT0FBTztRQUNQLDhCQUE4QjtRQUM5QixrQ0FBa0M7UUFDbEMsK0JBQStCO1FBQy9CLE1BQU07UUFDTiw0Q0FBNEM7UUFDNUMsb0NBQW9DO1FBQ3BDLDJDQUEyQztRQUMzQyxvREFBb0Q7UUFDcEQsbUJBQW1CO1FBQ25CLDRDQUE0QztRQUM1QyxvREFBb0Q7UUFDcEQsaUJBQWlCO1FBQ2pCLFFBQVE7UUFDUixJQUFJO1FBRUosNkJBQTZCO1FBQzdCLHlCQUF5QjtRQUN6Qiw2Q0FBNkM7UUFDN0Msa0RBQWtEO1FBQ2xELGdEQUFnRDtRQUNoRCw0REFBNEQ7UUFDNUQsWUFBWTtRQUNaLGtEQUFrRDtRQUNsRCx3REFBd0Q7UUFDeEQsZ0RBQWdEO1FBQ2hELHdDQUF3QztRQUN4QywyREFBMkQ7UUFDM0QsWUFBWTtRQUNaLFFBQVE7UUFDUixJQUFJO1FBRUosc0JBQXNCO1FBQ3RCckUsVUFBVXVEO1FBQ1ZuRCxpQkFBaUJpRTtRQUNqQiw4Q0FBOEM7UUFDOUM1QyxzQkFBc0I4QjtJQUMxQjtJQUVBLE1BQU1nQixjQUFjO1FBQ2hCLElBQUksQ0FBQ2xILHFCQUFxQixDQUFDYixTQUFTO1lBQ2hDO1FBQ0o7UUFFQSxNQUFNZ0ksYUFBeUI7WUFDM0I7Z0JBQUM7Z0JBQVE7Z0JBQVk7Z0JBQWU7YUFBTTtTQUM3QztRQUVEbkgsa0JBQ0s3QixNQUFNLENBQ0gsQ0FBQ21CLG1CQUNHQSxpQkFBaUJHLE1BQU0sS0FBSyxpQkFFbkMySCxPQUFPLENBQUMsQ0FBQzlIO2dCQUVGQSw4QkFLRUE7WUFOTixNQUFNeUMsY0FBY3JELGVBQ2hCWSxFQUFBQSwrQkFBQUEsaUJBQWlCNkIsVUFBVSxjQUEzQjdCLG1EQUFBQSw2QkFBNkJQLEVBQUUsS0FBSSxHQUNuQ0g7WUFFSixNQUFNeUksaUJBQWlCdEYsY0FDakIsR0FBNEJBLE9BQXpCQSxZQUFZdEUsU0FBUyxFQUFDLEtBQXVCLE9BQXBCc0UsWUFBWXJFLE9BQU8sSUFDL0M0QixFQUFBQSxnQ0FBQUEsaUJBQWlCNkIsVUFBVSxjQUEzQjdCLG9EQUFBQSw4QkFBNkJ5QixJQUFJLEtBQUk7WUFFM0NvRyxXQUFXdkMsSUFBSSxDQUFDO2dCQUNadEYsaUJBQWlCeUIsSUFBSTtnQkFDckI1QixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQ01oQixNQUFNLENBQ0osQ0FBQ2lCO3dCQUVHRTsyQkFEQUYsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRTCxFQUFFLE9BQ1ZPLG1DQUFBQSxpQkFBaUI0QixjQUFjLGNBQS9CNUIsdURBQUFBLGlDQUFpQ1AsRUFBRTttQkFFMUNQLEdBQUcsQ0FBQyxDQUFDWSxTQUFtQkEsT0FBT3BCLEtBQUssRUFDcENTLElBQUksQ0FBQyxVQUFTO2dCQUNuQjRJO2dCQUNBaEwsNkZBQWNBLENBQUNpRCxpQkFBaUJFLFNBQVM7YUFDNUM7UUFDTDtRQUVKbEQsa0VBQVNBLENBQUM2SztJQUNkO0lBRUEsTUFBTUcsY0FBYztRQUNoQixJQUFJLENBQUN0SCxxQkFBcUIsQ0FBQ2IsU0FBUztZQUNoQztRQUNKO1FBRUEsTUFBTW9JLFVBQWU7WUFBQztnQkFBQztnQkFBYTtnQkFBWTtnQkFBZTthQUFNO1NBQUM7UUFFdEUsTUFBTUMsT0FBWXhILGtCQUNiN0IsTUFBTSxDQUNILENBQUNtQixtQkFDR0EsaUJBQWlCRyxNQUFNLEtBQUssaUJBRW5DakIsR0FBRyxDQUFDLENBQUNjO2dCQUVFQSw4QkFLRUE7WUFOTixNQUFNeUMsY0FBY3JELGVBQ2hCWSxFQUFBQSwrQkFBQUEsaUJBQWlCNkIsVUFBVSxjQUEzQjdCLG1EQUFBQSw2QkFBNkJQLEVBQUUsS0FBSSxHQUNuQ0g7WUFFSixNQUFNeUksaUJBQWlCdEYsY0FDakIsR0FBNEJBLE9BQXpCQSxZQUFZdEUsU0FBUyxFQUFDLEtBQXVCLE9BQXBCc0UsWUFBWXJFLE9BQU8sSUFDL0M0QixFQUFBQSxnQ0FBQUEsaUJBQWlCNkIsVUFBVSxjQUEzQjdCLG9EQUFBQSw4QkFBNkJ5QixJQUFJLEtBQUk7WUFFM0MsT0FBTztnQkFDSHpCLGlCQUFpQnlCLElBQUk7Z0JBQ3JCNUIsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUNNaEIsTUFBTSxDQUNKLENBQUNpQjt3QkFFR0U7MkJBREFGLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUUwsRUFBRSxPQUNWTyxtQ0FBQUEsaUJBQWlCNEIsY0FBYyxjQUEvQjVCLHVEQUFBQSxpQ0FBaUNQLEVBQUU7bUJBRTFDUCxHQUFHLENBQUMsQ0FBQ1ksU0FBbUJBLE9BQU9wQixLQUFLLEVBQ3BDUyxJQUFJLENBQUMsVUFBUztnQkFDbkI0STtnQkFDQWhMLDZGQUFjQSxDQUFDaUQsaUJBQWlCRSxTQUFTO2FBQzVDO1FBQ0w7UUFFSmpELHVFQUFjQSxDQUFDO1lBQ1hnTDtZQUNBQztRQUNKO0lBQ0o7SUFFQSxNQUFNQyx3QkFBd0IsQ0FBQ2hJO1FBQzNCLE9BQVFBO1lBQ0osS0FBSztnQkFDRCxPQUFPO1lBQ1gsS0FBSztnQkFDRCxPQUFPO1lBQ1g7Z0JBQ0ksT0FBTztRQUNmO0lBQ0o7SUFFQSxNQUFNaUksMkJBQTJCLENBQzdCOUksVUFDQXlCLG9CQUVBNUQseUVBQWFBLENBQUM7WUFDVjtnQkFDSThELGFBQWE7Z0JBQ2JDLFFBQVE7d0JBQUMsRUFBRUMsTUFBTSxFQUFtQjt5Q0FDaEMsOERBQUMvRCxvRkFBbUJBO3dCQUFDK0QsUUFBUUE7d0JBQVF6QyxPQUFNOzs7Ozs7O2dCQUUvQzBDLE1BQU07d0JBQUMsRUFBRUMsR0FBRyxFQUFnQzt3QkFHcENyQiw4QkFJQUEsa0NBSWtCQSw2QkFDRkEsOEJBcUJQQSwrQkFhSUEsNkJBc0JBQTtvQkFuRWpCLE1BQU1BLG1CQUFxQ3FCLElBQUlDLFFBQVE7b0JBQ3ZELE1BQU1tQixjQUFjckQsZUFDaEJZLEVBQUFBLCtCQUFBQSxpQkFBaUI2QixVQUFVLGNBQTNCN0IsbURBQUFBLDZCQUE2QlAsRUFBRSxLQUFJLEdBQ25DSDtvQkFFSixNQUFNK0MsZ0JBQWdCMUMsaUJBQ2xCSyxFQUFBQSxtQ0FBQUEsaUJBQWlCNEIsY0FBYyxjQUEvQjVCLHVEQUFBQSxpQ0FBaUNQLEVBQUUsS0FBSSxHQUN2Q0k7b0JBR0osTUFBTXdJLGlCQUFnQnJJLDhCQUFBQSxpQkFBaUJFLFNBQVMsY0FBMUJGLGtEQUFBQSw0QkFBNEJHLE1BQU07b0JBQ3hELE1BQU1tSSxlQUFjdEksK0JBQUFBLGlCQUFpQkUsU0FBUyxjQUExQkYsbURBQUFBLDZCQUE0QnlFLEdBQUc7d0JBUzFDekU7b0JBUFQsTUFBTXVJLHlCQUNGLDhEQUFDaE0saURBQUlBO3dCQUNEaUYsTUFBTSx1QkFBMERaLE9BQW5DWixpQkFBaUJQLEVBQUUsRUFBQyxpQkFBMkJvQixPQUFaRCxVQUFTLEtBQTJCLE9BQXhCQyxhQUFhbkIsUUFBUTt3QkFDakdjLFdBQVd2QyxtREFBRUEsQ0FDVCwwQkFDQWtLLHNCQUFzQkU7a0NBRXpCckksQ0FBQUEseUJBQUFBLGlCQUFpQnlCLElBQUksY0FBckJ6QixvQ0FBQUEseUJBQ0csU0FBNEN4RCxPQUFuQ3dELGlCQUFpQlAsRUFBRSxFQUFDLGlCQUVOLE9BRnFCakQsNENBQUtBLENBQzdDd0QsaUJBQWlCMEIsT0FBTyxFQUMxQkMsTUFBTSxDQUFDOzs7Ozs7b0JBSXJCLHFCQUNJOzswQ0FFSSw4REFBQ0o7Z0NBQUlmLFdBQVU7O29DQUVWUixFQUFBQSxnQ0FBQUEsaUJBQWlCNkIsVUFBVSxjQUEzQjdCLG9EQUFBQSw4QkFBNkJQLEVBQUUsSUFBRyxtQkFDL0IsOERBQUNsQywwREFBTUE7d0NBQUNpRCxXQUFVO2tEQUNkLDRFQUFDaEQsa0VBQWNBOzRDQUFDZ0QsV0FBVTtzREFDckJ0QyxnQkFDR3VFLHdCQUFBQSxrQ0FBQUEsWUFBYXRFLFNBQVMsRUFDdEJzRSx3QkFBQUEsa0NBQUFBLFlBQWFyRSxPQUFPOzs7Ozs7Ozs7OztrREFLcEMsOERBQUNtRDt3Q0FBSWYsV0FBVTs7NENBQ1YrSDs0Q0FFQXZJLEVBQUFBLDhCQUFBQSxpQkFBaUIwQyxTQUFTLGNBQTFCMUMsa0RBQUFBLDRCQUE0QlAsRUFBRSxJQUFHLG1CQUM5Qiw4REFBQzhCO2dEQUFJZixXQUFVOzBEQUNYLDRFQUFDakUsaURBQUlBO29EQUNEaUYsTUFBTSxzQkFBb0QsT0FBOUJ4QixpQkFBaUIwQyxTQUFTLENBQUNqRCxFQUFFO29EQUN6RGUsV0FBVTs4REFFTlIsaUJBQWlCMEMsU0FBUyxDQUNyQkMsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBU2pDLDhEQUFDcEI7Z0NBQUlmLFdBQVU7O2tEQUNYLDhEQUFDZTt3Q0FBSWYsV0FBVTtrREFDVitIOzs7Ozs7a0RBR0wsOERBQUNoSDt3Q0FBSWYsV0FBVTtrREFDVlIsRUFBQUEsb0NBQUFBLGlCQUFpQjRCLGNBQWMsY0FBL0I1Qix3REFBQUEsa0NBQWlDUCxFQUFFLElBQ2hDLG1CQUNBLDhEQUFDbEQsaURBQUlBOzRDQUNEaUYsTUFBTSxtQkFBc0QsT0FBbkN4QixpQkFBaUI0QixjQUFjLENBQUNuQyxFQUFFOzRDQUMzRGUsV0FBVTtzREFFTlIsaUJBQWlCNEIsY0FBYyxDQUMxQmxELEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBUTFDO2dCQUNBb0QsV0FBVyxDQUNQQyxNQUNBQzt3QkFFZUQsZ0JBQ0FDO29CQURmLE1BQU1DLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ULFFBQVEsY0FBZFMscUNBQUFBLGVBQWdCTixJQUFJLEtBQUk7b0JBQ3ZDLE1BQU1TLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1WLFFBQVEsY0FBZFUscUNBQUFBLGVBQWdCUCxJQUFJLEtBQUk7b0JBQ3ZDLE9BQU9RLE9BQU9FLGFBQWEsQ0FBQ0Q7Z0JBQ2hDO1lBQ0o7WUFDQTtnQkFDSWpCLGFBQWE7Z0JBQ2JDLFFBQVE7d0JBQUMsRUFBRUMsTUFBTSxFQUFtQjt5Q0FDaEMsOERBQUMvRCxvRkFBbUJBO3dCQUFDK0QsUUFBUUE7d0JBQVF6QyxPQUFNOzs7Ozs7O2dCQUUvQzBELGVBQWU7Z0JBQ2ZJLFlBQVk7Z0JBQ1pwQixNQUFNO3dCQUFDLEVBQUVDLEdBQUcsRUFBZ0M7d0JBR3BDckIsa0NBTUFBLG1DQU1LQTtvQkFkVCxNQUFNQSxtQkFBbUJxQixJQUFJQyxRQUFRO29CQUNyQyxNQUFNZSxnQkFBZ0IxQyxpQkFDbEJLLEVBQUFBLG1DQUFBQSxpQkFBaUI0QixjQUFjLGNBQS9CNUIsdURBQUFBLGlDQUFpQ1AsRUFBRSxLQUFJLEdBQ3ZDSTtvQkFHSiw0QkFBNEI7b0JBQzVCLE1BQU0ySSxpQkFBaUJ6SCxrQkFDbkJmLEVBQUFBLG9DQUFBQSxpQkFBaUI0QixjQUFjLGNBQS9CNUIsd0RBQUFBLGtDQUFpQ1AsRUFBRSxLQUFJLEdBQ3ZDNEM7b0JBR0oscUJBQ0k7a0NBQ0tyQyxFQUFBQSxvQ0FBQUEsaUJBQWlCNEIsY0FBYyxjQUEvQjVCLHdEQUFBQSxrQ0FBaUNQLEVBQUUsSUFBRyxtQkFDbkMsOERBQUNoQywwRkFBcUJBOzRCQUNsQnFDLFFBQVEwSTs0QkFDUmxHLFVBQ0l0QyxpQkFBaUI0QixjQUFjLENBQUNuQyxFQUFFOzRCQUV0QzhDLGFBQ0l2QyxpQkFBaUI0QixjQUFjLENBQUNsRCxLQUFLLElBQ3JDOzs7Ozs7O2dCQU14QjtnQkFDQW9ELFdBQVcsQ0FDUEMsTUFDQUM7d0JBRWVELCtCQUFBQSxnQkFDQUMsK0JBQUFBO29CQURmLE1BQU1DLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ULFFBQVEsY0FBZFMsc0NBQUFBLGdDQUFBQSxlQUFnQkgsY0FBYyxjQUE5Qkcsb0RBQUFBLDhCQUFnQ3JELEtBQUssS0FBSTtvQkFDeEQsTUFBTXdELFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1WLFFBQVEsY0FBZFUsc0NBQUFBLGdDQUFBQSxlQUFnQkosY0FBYyxjQUE5Qkksb0RBQUFBLDhCQUFnQ3RELEtBQUssS0FBSTtvQkFDeEQsT0FBT3VELE9BQU9FLGFBQWEsQ0FBQ0Q7Z0JBQ2hDO1lBQ0o7WUFDQTtnQkFDSWpCLGFBQWE7Z0JBQ2JDLFFBQVE7d0JBQUMsRUFBRUMsTUFBTSxFQUFtQjt5Q0FDaEMsOERBQUMvRCxvRkFBbUJBO3dCQUFDK0QsUUFBUUE7d0JBQVF6QyxPQUFNOzs7Ozs7O2dCQUUvQzBELGVBQWU7Z0JBQ2ZJLFlBQVk7Z0JBQ1pwQixNQUFNO3dCQUFDLEVBQUVDLEdBQUcsRUFBZ0M7d0JBR3BDckIsOEJBTUtBO29CQVJULE1BQU1BLG1CQUFtQnFCLElBQUlDLFFBQVE7b0JBQ3JDLE1BQU1tQixjQUFjckQsZUFDaEJZLEVBQUFBLCtCQUFBQSxpQkFBaUI2QixVQUFVLGNBQTNCN0IsbURBQUFBLDZCQUE2QlAsRUFBRSxLQUFJLEdBQ25DSDtvQkFHSixxQkFDSTtrQ0FDS1UsRUFBQUEsZ0NBQUFBLGlCQUFpQjZCLFVBQVUsY0FBM0I3QixvREFBQUEsOEJBQTZCUCxFQUFFLElBQUcsbUJBQy9CLDhEQUFDOEI7NEJBQUlmLFdBQVU7OzhDQUNYLDhEQUFDakQsMERBQU1BO29DQUFDaUQsV0FBVTs4Q0FDZCw0RUFBQ2hELGtFQUFjQTt3Q0FBQ2dELFdBQVU7a0RBQ3JCdEMsZ0JBQ0d1RSx3QkFBQUEsa0NBQUFBLFlBQWF0RSxTQUFTLEVBQ3RCc0Usd0JBQUFBLGtDQUFBQSxZQUFhckUsT0FBTzs7Ozs7Ozs7Ozs7OENBSWhDLDhEQUFDN0IsaURBQUlBO29DQUNEaUYsTUFBTSxpQkFBZ0QsT0FBL0J4QixpQkFBaUI2QixVQUFVLENBQUNwQyxFQUFFO29DQUNyRGUsV0FBVTs4Q0FDVFIsaUJBQWlCNkIsVUFBVSxDQUFDSixJQUFJOzs7Ozs7Ozs7Ozs7O2dCQU16RDtnQkFDQUssV0FBVyxDQUNQQyxNQUNBQzt3QkFFZUQsMkJBQUFBLGdCQUNBQywyQkFBQUE7b0JBRGYsTUFBTUMsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTVQsUUFBUSxjQUFkUyxzQ0FBQUEsNEJBQUFBLGVBQWdCRixVQUFVLGNBQTFCRSxnREFBQUEsMEJBQTRCTixJQUFJLEtBQUk7b0JBQ25ELE1BQU1TLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1WLFFBQVEsY0FBZFUsc0NBQUFBLDRCQUFBQSxlQUFnQkgsVUFBVSxjQUExQkcsZ0RBQUFBLDBCQUE0QlAsSUFBSSxLQUFJO29CQUNuRCxPQUFPUSxPQUFPRSxhQUFhLENBQUNEO2dCQUNoQztZQUNKO1lBQ0E7Z0JBQ0lqQixhQUFhO2dCQUNiQyxRQUFRO2dCQUNSa0IsZUFBZTtnQkFDZkksWUFBWTtnQkFDWnBCLE1BQU07d0JBQUMsRUFBRUMsR0FBRyxFQUFnQzt3QkFJL0JyQjtvQkFIVCxNQUFNQSxtQkFBbUJxQixJQUFJQyxRQUFRO29CQUNyQyxxQkFDSTtrQ0FDS3RCLEVBQUFBLDhCQUFBQSxpQkFBaUIwQyxTQUFTLGNBQTFCMUMsa0RBQUFBLDRCQUE0QlAsRUFBRSxJQUFHLGtCQUM5Qiw4REFBQ2xELGlEQUFJQTs0QkFDRGlGLE1BQU0sc0JBQW9ELE9BQTlCeEIsaUJBQWlCMEMsU0FBUyxDQUFDakQsRUFBRTs0QkFDekRlLFdBQVU7c0NBQ1RSLGlCQUFpQjBDLFNBQVMsQ0FBQ0MsSUFBSTs7Ozs7aURBR3BDLDhEQUFDcEM7c0NBQUs7Ozs7Ozs7Z0JBSXRCO1lBQ0o7WUFDQTtnQkFDSVUsYUFBYTtnQkFDYkMsUUFBUTt3QkFBQyxFQUFFQyxNQUFNLEVBQW1CO3lDQUNoQyw4REFBQy9ELG9GQUFtQkE7d0JBQUMrRCxRQUFRQTt3QkFBUXpDLE9BQU07Ozs7Ozs7Z0JBRS9DMEQsZUFBZTtnQkFDZmhCLE1BQU07d0JBQUMsRUFBRUMsR0FBRyxFQUFnQzt3QkFPbEJyQiw2QkFDRkE7b0JBUHBCLE1BQU1BLG1CQUFtQnFCLElBQUlDLFFBQVE7b0JBRXJDLElBQUksQ0FBQ3RCLGtCQUFrQjt3QkFDbkIscUJBQU8sOERBQUN1QjtzQ0FBSTs7Ozs7O29CQUNoQjtvQkFFQSxNQUFNOEcsaUJBQWdCckksOEJBQUFBLGlCQUFpQkUsU0FBUyxjQUExQkYsa0RBQUFBLDRCQUE0QkcsTUFBTTtvQkFDeEQsTUFBTW1JLGVBQWN0SSwrQkFBQUEsaUJBQWlCRSxTQUFTLGNBQTFCRixtREFBQUEsNkJBQTRCeUUsR0FBRztvQkFFbkQscUJBQ0k7a0NBQ0s0RCxrQkFBa0IsU0FDZixDQUFDdkUsRUFBRSxDQUFDLFlBQVksaUJBQ1osOERBQUN2Qzs0QkFDR2YsV0FBVywwSEFDNkQsT0FBMUQ2SCxrQkFBa0IsU0FBUyw0QkFBNEIsSUFBRztzQ0FFdkVDLGNBQWMsQ0FBQyxJQUFJOzs7OztpREFHeEIsOERBQUN2STs0QkFDR0Msa0JBQWtCQTs7Ozs7aURBSTFCLDhEQUFDRDs0QkFDR0Msa0JBQWtCQTs7Ozs7OztnQkFLdEM7Z0JBQ0E4QixXQUFXLENBQ1BDLE1BQ0FDO3dCQUVlRCwwQkFBQUEsZ0JBQ0FDLDBCQUFBQTtvQkFEZixNQUFNQyxTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNVCxRQUFRLGNBQWRTLHNDQUFBQSwyQkFBQUEsZUFBZ0I3QixTQUFTLGNBQXpCNkIsK0NBQUFBLHlCQUEyQnpCLElBQUksS0FBSTtvQkFDbEQsTUFBTTRCLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1WLFFBQVEsY0FBZFUsc0NBQUFBLDJCQUFBQSxlQUFnQjlCLFNBQVMsY0FBekI4QiwrQ0FBQUEseUJBQTJCMUIsSUFBSSxLQUFJO29CQUNsRCxPQUFPMkIsT0FBT0UsYUFBYSxDQUFDRDtnQkFDaEM7WUFDSjtTQUNIO0lBRUwsTUFBTWxCLFVBQVVvSCx5QkFBeUI5SSxZQUFZLEVBQUUsRUFBRXlCO0lBRXpELDZDQUE2QztJQUM3QyxNQUFNMEgsMEJBQW1ELENBQ3JEekk7WUFXc0JBO1FBVHRCLDJDQUEyQztRQUMzQyxJQUNJQSxpQkFBaUJHLE1BQU0sS0FBSyxlQUM1QkgsaUJBQWlCaUYsUUFBUSxJQUN6QmpGLGlCQUFpQkcsTUFBTSxLQUFLLGlCQUM5QjtZQUNFLE9BQU87UUFDWDtRQUVBLE1BQU1rSSxpQkFBZ0JySSw4QkFBQUEsaUJBQWlCRSxTQUFTLGNBQTFCRixrREFBQUEsNEJBQTRCRyxNQUFNO1FBRXhELHVEQUF1RDtRQUN2RCxPQUFRa0k7WUFDSixLQUFLO2dCQUNELE9BQU8sVUFBVSxtQkFBbUI7O1lBQ3hDLEtBQUs7Z0JBQ0QsT0FBTyxXQUFXLHNCQUFzQjs7WUFDNUMsS0FBSztZQUNMLEtBQUs7WUFDTDtnQkFDSSxPQUFPLFNBQVMsa0JBQWtCOztRQUMxQztJQUNKO0lBRUEscUJBQ0k7OzBCQUNJLDhEQUFDL0ssbUVBQVVBO2dCQUNQb0IsT0FBTTtnQkFDTmdLLG9CQUNJLDhEQUFDOUssbUVBQXNCQTtvQkFBQzRDLFdBQVU7Ozs7OztnQkFFdENtSSx1QkFBUyw4REFBQ3RMLHdHQUF3QkE7Ozs7O2dCQUNsQ3VMLGdCQUFlOzs7Ozs7MEJBRW5CLDhEQUFDckg7Z0JBQUlmLFdBQVU7MEJBQ1ZFLHFCQUFxQmIsd0JBQ2xCLDhEQUFDM0MsaUVBQVNBO29CQUNOOEQsU0FBU0E7b0JBQ1Q0QixNQUFNLDZCQUFzQyxFQUFFO29CQUM5Q0MsVUFBVTtvQkFDVmdHLFVBQVVuQztvQkFDVm9DLFdBQVdMOzs7Ozt5Q0FHZiw4REFBQ25NLGdFQUFhQTs7Ozs7Ozs7Ozs7O0FBS2xDO0lBbndCd0J5Rzs7UUFZSHBHLHdEQUFXQTtRQUNQQyw0REFBZUE7UUFDTmMsMkVBQWlCQTtRQUNwQ0MsNkVBQWNBO1FBdUJRdEIseURBQVlBO1FBaUdmQSx5REFBWUE7OztNQXZJdEIwRyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL21haW50ZW5hbmNlL2xpc3QvbGlzdC50c3g/NmExYiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnXHJcbmltcG9ydCB7IFRhYmxlU2tlbGV0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvc2tlbGV0b25zJ1xyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXHJcblxyXG5pbXBvcnQgZGF5anMgZnJvbSAnZGF5anMnXHJcbmltcG9ydCB7IGlzRW1wdHksIHRyaW0gfSBmcm9tICdsb2Rhc2gnXHJcbmltcG9ydCB7IHVzZVBhdGhuYW1lLCB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXHJcbmltcG9ydCB7IGdldFBlcm1pc3Npb25zLCBoYXNQZXJtaXNzaW9uIH0gZnJvbSAnQC9hcHAvaGVscGVycy91c2VySGVscGVyJ1xyXG5pbXBvcnQgeyBkdWVTdGF0dXNMYWJlbCB9IGZyb20gJy4uLy4uL3JlcG9ydGluZy9tYWludGVuYW5jZS1zdGF0dXMtYWN0aXZpdHktcmVwb3J0J1xyXG5pbXBvcnQgeyBleHBvcnRDc3YgfSBmcm9tICdAL2FwcC9oZWxwZXJzL2NzdkhlbHBlcidcclxuaW1wb3J0IHsgZXhwb3J0UGRmVGFibGUgfSBmcm9tICdAL2FwcC9oZWxwZXJzL3BkZkhlbHBlcidcclxuaW1wb3J0IHtcclxuICAgIERhdGFUYWJsZSxcclxuICAgIGNyZWF0ZUNvbHVtbnMsXHJcbiAgICBSb3dTdGF0dXNFdmFsdWF0b3IsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL2ZpbHRlcmVkVGFibGUnXHJcbmltcG9ydCB7IERhdGFUYWJsZVNvcnRIZWFkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZGF0YS10YWJsZS1zb3J0LWhlYWRlcidcclxuaW1wb3J0IHsgTWFpbnRlbmFuY2VGaWx0ZXJBY3Rpb25zIH0gZnJvbSAnQC9jb21wb25lbnRzL2ZpbHRlci9jb21wb25lbnRzL21haW50ZW5hbmNlLWFjdGlvbnMnXHJcbmltcG9ydCB7IExpc3RIZWFkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGlzdC1oZWFkZXInXHJcbmltcG9ydCB7IEF2YXRhciwgQXZhdGFyRmFsbGJhY2sgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYXZhdGFyJ1xyXG5pbXBvcnQgeyBWZXNzZWxMb2NhdGlvbkRpc3BsYXkgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdmVzc2VsLWxvY2F0aW9uLWRpc3BsYXknXHJcbmltcG9ydCB7IHVzZVZlc3NlbEljb25EYXRhIH0gZnJvbSAnQC9hcHAvbGliL3Zlc3NlbC1pY29uLWhlbHBlcidcclxuaW1wb3J0IHsgdXNlQnJlYWtwb2ludHMgfSBmcm9tICdAL2NvbXBvbmVudHMvaG9va3MvdXNlQnJlYWtwb2ludHMnXHJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXHJcbmltcG9ydCB7IFNlYWxvZ3NNYWludGVuYW5jZUljb24gfSBmcm9tICdAL2FwcC9saWIvaWNvbnMnXHJcbmltcG9ydCB7XHJcbiAgICBSZWFkQ29tcG9uZW50TWFpbnRlbmFuY2VDaGVja0xpc3QsXHJcbiAgICBSZWFkU2VhTG9nc01lbWJlcnMsXHJcbiAgICBSZWFkVmVzc2VscyxcclxufSBmcm9tICcuL3F1ZXJpZXMnXHJcbmltcG9ydCBWZXNzZWxNb2RlbCBmcm9tICdAL2FwcC9vZmZsaW5lL21vZGVscy92ZXNzZWwnXHJcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9hcHAvbGliL3V0aWxzJ1xyXG5cclxuLy8gSGVscGVyIGZ1bmN0aW9uc1xyXG5jb25zdCBnZXRDcmV3SW5pdGlhbHMgPSAoZmlyc3ROYW1lPzogc3RyaW5nLCBzdXJuYW1lPzogc3RyaW5nKTogc3RyaW5nID0+IHtcclxuICAgIGlmICghZmlyc3ROYW1lICYmICFzdXJuYW1lKSByZXR1cm4gJz8/J1xyXG4gICAgY29uc3QgZmlyc3QgPSBmaXJzdE5hbWU/LmNoYXJBdCgwKT8udG9VcHBlckNhc2UoKSB8fCAnJ1xyXG4gICAgY29uc3QgbGFzdCA9IHN1cm5hbWU/LmNoYXJBdCgwKT8udG9VcHBlckNhc2UoKSB8fCAnJ1xyXG4gICAgcmV0dXJuIGAke2ZpcnN0fSR7bGFzdH1gIHx8ICc/PydcclxufVxyXG5cclxuY29uc3QgZ2V0VmVzc2VsSW5pdGlhbHMgPSAodGl0bGU/OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xyXG4gICAgaWYgKCF0aXRsZSkgcmV0dXJuICc/PydcclxuICAgIGNvbnN0IHdvcmRzID0gdGl0bGUuc3BsaXQoJyAnKS5maWx0ZXIoKHdvcmQpID0+IHdvcmQubGVuZ3RoID4gMClcclxuICAgIGlmICh3b3Jkcy5sZW5ndGggPT09IDEpIHtcclxuICAgICAgICByZXR1cm4gd29yZHNbMF0uc3Vic3RyaW5nKDAsIDIpLnRvVXBwZXJDYXNlKClcclxuICAgIH1cclxuICAgIHJldHVybiB3b3Jkc1xyXG4gICAgICAgIC5zbGljZSgwLCAyKVxyXG4gICAgICAgIC5tYXAoKHdvcmQpID0+IHdvcmQuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkpXHJcbiAgICAgICAgLmpvaW4oJycpXHJcbn1cclxuXHJcbmNvbnN0IGdldENyZXdEZXRhaWxzID0gKGFzc2lnbmVkVG9JRDogbnVtYmVyLCBjcmV3SW5mbz86IENyZXdNZW1iZXJbXSkgPT4ge1xyXG4gICAgcmV0dXJuIGNyZXdJbmZvPy5maW5kKChjcmV3KSA9PiBjcmV3LmlkID09PSBhc3NpZ25lZFRvSUQudG9TdHJpbmcoKSlcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGdldFZlc3NlbERldGFpbHMgPSAodmVzc2VsSUQ6IG51bWJlciwgdmVzc2Vscz86IFZlc3NlbFtdKSA9PiB7XHJcbiAgICByZXR1cm4gdmVzc2Vscz8uZmluZCgodmVzc2VsKSA9PiB2ZXNzZWwuaWQgPT09IHZlc3NlbElEKVxyXG59XHJcblxyXG4vLyBUeXBlIGRlZmluaXRpb25zXHJcbmludGVyZmFjZSBNYWludGVuYW5jZUNoZWNrIHtcclxuICAgIGlkOiBudW1iZXJcclxuICAgIGFzc2lnbmVkVG86IHtcclxuICAgICAgICBpZDogbnVtYmVyXHJcbiAgICAgICAgbmFtZTogc3RyaW5nXHJcbiAgICB9XHJcbiAgICBiYXNpY0NvbXBvbmVudDoge1xyXG4gICAgICAgIGlkOiBudW1iZXJcclxuICAgICAgICB0aXRsZTogc3RyaW5nIHwgbnVsbFxyXG4gICAgfVxyXG4gICAgaW52ZW50b3J5OiB7XHJcbiAgICAgICAgaWQ6IG51bWJlclxyXG4gICAgICAgIGl0ZW06IHN0cmluZyB8IG51bGxcclxuICAgIH1cclxuICAgIHN0YXR1czogc3RyaW5nXHJcbiAgICByZWN1cnJpbmdJRDogbnVtYmVyXHJcbiAgICBuYW1lOiBzdHJpbmdcclxuICAgIGNyZWF0ZWQ6IHN0cmluZyAvLyBlLmcuLCBcIjIwMjUtMDYtMDkgMDI6Mzg6MzZcIlxyXG4gICAgc2V2ZXJpdHk6IHN0cmluZyAvLyBlLmcuLCBcIkxvd1wiXHJcbiAgICBpc092ZXJEdWU6IHtcclxuICAgICAgICBzdGF0dXM6IHN0cmluZyAvLyBlLmcuLCBcIk1lZGl1bVwiXHJcbiAgICAgICAgZGF5czogc3RyaW5nIC8vIGUuZy4sIFwiT3BlblwiXHJcbiAgICAgICAgaWdub3JlOiBib29sZWFuXHJcbiAgICAgICAgZGF5OiBudW1iZXJcclxuICAgIH1cclxuICAgIGNvbW1lbnRzOiBzdHJpbmcgfCBudWxsXHJcbiAgICB3b3JrT3JkZXJOdW1iZXI6IHN0cmluZyB8IG51bGxcclxuICAgIHN0YXJ0RGF0ZTogc3RyaW5nIC8vIGUuZy4sIFwiMjAyNS0wNi0wOCAwMDowMDowMFwiXHJcbiAgICBleHBpcmVzOiBzdHJpbmcgfCBudWxsXHJcbiAgICBtYWludGVuYW5jZUNhdGVnb3J5SUQ6IG51bWJlclxyXG59XHJcblxyXG50eXBlIE1haW50ZW5hbmNlUm93VHlwZXMgPSB7XHJcbiAgICBpZDogc3RyaW5nXHJcbiAgICBpbmRleDogbnVtYmVyXHJcbiAgICBvcmlnaW5hbDogTWFpbnRlbmFuY2VDaGVja1xyXG4gICAgZGVwdGg6IG51bWJlclxyXG4gICAgX3ZhbHVlc0NhY2hlOiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPlxyXG4gICAgX3VuaXF1ZVZhbHVlc0NhY2hlOiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPlxyXG4gICAgc3ViUm93czogYW55W10gLy8gYWRqdXN0IGlmIHN1Yi1yb3cgc3RydWN0dXJlIGlzIGtub3duXHJcbiAgICBjb2x1bW5GaWx0ZXJzOiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPlxyXG4gICAgY29sdW1uRmlsdGVyc01ldGE6IFJlY29yZDxzdHJpbmcsIHVua25vd24+XHJcbiAgICBfZ3JvdXBpbmdWYWx1ZXNDYWNoZTogUmVjb3JkPHN0cmluZywgdW5rbm93bj5cclxufVxyXG5cclxuaW50ZXJmYWNlIFZlc3NlbCB7XHJcbiAgICBpZDogbnVtYmVyXHJcbiAgICB0aXRsZTogc3RyaW5nXHJcbiAgICBhcmNoaXZlZD86IGJvb2xlYW5cclxufVxyXG5cclxuaW50ZXJmYWNlIENyZXdNZW1iZXIge1xyXG4gICAgaWQ6IHN0cmluZ1xyXG4gICAgZmlyc3ROYW1lOiBzdHJpbmdcclxuICAgIHN1cm5hbWU6IHN0cmluZ1xyXG59XHJcblxyXG5pbnRlcmZhY2UgU2VhcmNoRmlsdGVyIHtcclxuICAgIGJhc2ljQ29tcG9uZW50SUQ/OiB7IGVxPzogbnVtYmVyOyBpbj86IG51bWJlcltdIH1cclxuICAgIHN0YXR1cz86IHsgZXE/OiBzdHJpbmc7IGluPzogc3RyaW5nW10gfVxyXG4gICAgYXNzaWduZWRUb0lEPzogeyBlcT86IG51bWJlcjsgaW4/OiBudW1iZXJbXSB9XHJcbiAgICBleHBpcmVzPzogeyBndGU/OiBzdHJpbmc7IGx0ZT86IHN0cmluZyB9XHJcbiAgICBtYWludGVuYW5jZUNhdGVnb3J5SUQ/OiB7IGVxPzogbnVtYmVyOyBpbj86IG51bWJlcltdIH1cclxuICAgIGlzT3ZlckR1ZT86IHsgZGF5PzogeyBsdD86IG51bWJlciB9IH1cclxuICAgIHRhc2tUeXBlPzogeyBlcT86IHN0cmluZyB9XHJcbiAgICBuYW1lPzogeyBjb250YWlucz86IHN0cmluZyB9XHJcbn1cclxuXHJcbi8vIFN0YXR1cyBiYWRnZSBjb21wb25lbnQgZm9sbG93aW5nIFVJIHN0YW5kYXJkc1xyXG5leHBvcnQgY29uc3QgU3RhdHVzQmFkZ2UgPSAoe1xyXG4gICAgbWFpbnRlbmFuY2VDaGVjayxcclxufToge1xyXG4gICAgbWFpbnRlbmFuY2VDaGVjazogTWFpbnRlbmFuY2VDaGVja1xyXG59KSA9PiB7XHJcbiAgICBjb25zdCBpc092ZXJkdWUgPSBtYWludGVuYW5jZUNoZWNrPy5pc092ZXJEdWU/LnN0YXR1cyA9PT0gJ0hpZ2gnXHJcblxyXG4gICAgLy8gR2V0IHN0YXR1cyB0ZXh0XHJcbiAgICBsZXQgc3RhdHVzVGV4dCA9ICcnXHJcbiAgICBpZiAoXHJcbiAgICAgICAgbWFpbnRlbmFuY2VDaGVjaz8uaXNPdmVyRHVlPy5zdGF0dXMgJiZcclxuICAgICAgICBbJ0hpZ2gnLCAnTWVkaXVtJywgJ0xvdyddLmluY2x1ZGVzKG1haW50ZW5hbmNlQ2hlY2suaXNPdmVyRHVlLnN0YXR1cylcclxuICAgICkge1xyXG4gICAgICAgIHN0YXR1c1RleHQgPSBtYWludGVuYW5jZUNoZWNrPy5pc092ZXJEdWU/LmRheXNcclxuICAgIH0gZWxzZSBpZiAoXHJcbiAgICAgICAgbWFpbnRlbmFuY2VDaGVjaz8uaXNPdmVyRHVlPy5zdGF0dXMgPT09ICdDb21wbGV0ZWQnICYmXHJcbiAgICAgICAgbWFpbnRlbmFuY2VDaGVjaz8uaXNPdmVyRHVlPy5kYXlzID09PSAnU2F2ZSBBcyBEcmFmdCdcclxuICAgICkge1xyXG4gICAgICAgIHN0YXR1c1RleHQgPSBtYWludGVuYW5jZUNoZWNrPy5pc092ZXJEdWU/LmRheXNcclxuICAgIH0gZWxzZSBpZiAobWFpbnRlbmFuY2VDaGVjaz8uaXNPdmVyRHVlPy5zdGF0dXMgPT09ICdVcGNvbWluZycpIHtcclxuICAgICAgICBzdGF0dXNUZXh0ID0gbWFpbnRlbmFuY2VDaGVjaz8uaXNPdmVyRHVlPy5kYXlzXHJcbiAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgIG1haW50ZW5hbmNlQ2hlY2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnQ29tcGxldGVkJyAmJlxyXG4gICAgICAgIGlzRW1wdHkobWFpbnRlbmFuY2VDaGVjaz8uaXNPdmVyRHVlPy5kYXlzKVxyXG4gICAgKSB7XHJcbiAgICAgICAgc3RhdHVzVGV4dCA9IG1haW50ZW5hbmNlQ2hlY2s/LmlzT3ZlckR1ZT8uc3RhdHVzXHJcbiAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgIG1haW50ZW5hbmNlQ2hlY2s/LmlzT3ZlckR1ZT8uc3RhdHVzID09PSAnQ29tcGxldGVkJyAmJlxyXG4gICAgICAgICFpc0VtcHR5KG1haW50ZW5hbmNlQ2hlY2s/LmlzT3ZlckR1ZT8uZGF5cykgJiZcclxuICAgICAgICBtYWludGVuYW5jZUNoZWNrPy5pc092ZXJEdWU/LmRheXMgIT09ICdTYXZlIEFzIERyYWZ0J1xyXG4gICAgKSB7XHJcbiAgICAgICAgc3RhdHVzVGV4dCA9IG1haW50ZW5hbmNlQ2hlY2s/LmlzT3ZlckR1ZT8uZGF5c1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE9ubHkgYXBwbHkgc3R5bGluZyB0byBvdmVyZHVlIGl0ZW1zLCBvdGhlcnMgYXJlIHBsYWluIHRleHRcclxuICAgIGlmIChpc092ZXJkdWUpIHtcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhbGVydCB3LWZpdCBpbmxpbmUtYmxvY2sgdGV4dC1ub3dyYXAgcm91bmRlZC1tZCB0ZXh0LXNtIHhzOnRleHQtYmFzZSBweS0wLjUgcHgtMiB4czpweC0zIHhzOnB5LTFcIj5cclxuICAgICAgICAgICAgICAgIHtzdGF0dXNUZXh0fVxyXG4gICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgKVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ub3dyYXAgdGV4dC1zbSB4czp0ZXh0LWJhc2VcIj57c3RhdHVzVGV4dH08L3NwYW4+XHJcbiAgICApXHJcbn1cclxuXHJcbi8vIFJldXNhYmxlIE1haW50ZW5hbmNlVGFibGUgY29tcG9uZW50IHRoYXQgYWNjZXB0cyBwcm9wc1xyXG5leHBvcnQgZnVuY3Rpb24gTWFpbnRlbmFuY2VUYWJsZSh7XHJcbiAgICBtYWludGVuYW5jZUNoZWNrcyxcclxuICAgIHZlc3NlbHMsXHJcbiAgICBjcmV3SW5mbyxcclxuICAgIHNob3dWZXNzZWwgPSBmYWxzZSxcclxufToge1xyXG4gICAgbWFpbnRlbmFuY2VDaGVja3M6IE1haW50ZW5hbmNlQ2hlY2tbXVxyXG4gICAgdmVzc2VsczogVmVzc2VsW11cclxuICAgIGNyZXdJbmZvOiBDcmV3TWVtYmVyW11cclxuICAgIHNob3dWZXNzZWw/OiBib29sZWFuXHJcbn0pIHtcclxuICAgIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxyXG4gICAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKClcclxuICAgIGNvbnN0IHsgdmVzc2VsSWNvbkRhdGEsIGdldFZlc3NlbFdpdGhJY29uIH0gPSB1c2VWZXNzZWxJY29uRGF0YSgpXHJcbiAgICBjb25zdCBjb2x1bW5zID0gY3JlYXRlQ29sdW1ucyhbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3RpdGxlJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJUaXRsZVwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogTWFpbnRlbmFuY2VSb3dUeXBlcyB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBtYWludGVuYW5jZUNoZWNrOiBNYWludGVuYW5jZUNoZWNrID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC9tYWludGVuYW5jZT90YXNrSUQ9JHttYWludGVuYW5jZUNoZWNrLmlkfSZyZWRpcmVjdF90bz0ke3BhdGhuYW1lfT8ke3NlYXJjaFBhcmFtcy50b1N0cmluZygpfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLm5hbWUgPz9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYFRhc2sgIyR7bWFpbnRlbmFuY2VDaGVjay5pZH0gKE5vIE5hbWUpIC0gJHtkYXlqcyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2suY3JlYXRlZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS5mb3JtYXQoJ0REL01NL1lZWVknKX1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBNb2JpbGU6IFNob3cgbG9jYXRpb24gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHshc2hvd1Zlc3NlbCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLmJhc2ljQ29tcG9uZW50Py5pZCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC92ZXNzZWwvaW5mbz9pZD0ke21haW50ZW5hbmNlQ2hlY2suYmFzaWNDb21wb25lbnQuaWR9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIiBob3Zlcjp0ZXh0LWN1cmlvdXMtYmx1ZS00MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLmJhc2ljQ29tcG9uZW50LnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIE1vYmlsZTogU2hvdyBhc3NpZ25lZCB0byBhbmQgc3RhdHVzICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLmFzc2lnbmVkVG8/LmlkID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtb3V0ZXItc3BhY2UtNDAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBc3NpZ25lZCB0bzp7JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL2NyZXcvaW5mbz9pZD0ke21haW50ZW5hbmNlQ2hlY2suYXNzaWduZWRUby5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1jdXJpb3VzLWJsdWUtNDAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWFpbnRlbmFuY2VDaGVjay5hc3NpZ25lZFRvLm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTdGF0dXNCYWRnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrPXttYWludGVuYW5jZUNoZWNrfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKFxyXG4gICAgICAgICAgICAgICAgcm93QTogTWFpbnRlbmFuY2VSb3dUeXBlcyxcclxuICAgICAgICAgICAgICAgIHJvd0I6IE1haW50ZW5hbmNlUm93VHlwZXMsXHJcbiAgICAgICAgICAgICkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/Lm5hbWUgfHwgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy5uYW1lIHx8ICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgLi4uKHNob3dWZXNzZWxcclxuICAgICAgICAgICAgPyBbXVxyXG4gICAgICAgICAgICA6IFtcclxuICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdsb2NhdGlvbicsXHJcbiAgICAgICAgICAgICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2x1bW49e2NvbHVtbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJMb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnbGVmdCcgYXMgY29uc3QsXHJcbiAgICAgICAgICAgICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IE1haW50ZW5hbmNlUm93VHlwZXMgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG1haW50ZW5hbmNlQ2hlY2sgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2ZXNzZWxEZXRhaWxzID0gZ2V0VmVzc2VsRGV0YWlscyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay5iYXNpY0NvbXBvbmVudD8uaWQgfHwgMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VscyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmJsb2NrXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWFpbnRlbmFuY2VDaGVjay5iYXNpY0NvbXBvbmVudD8uaWQgPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VmVzc2VsTG9jYXRpb25EaXNwbGF5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbD17dmVzc2VsRGV0YWlsc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsSWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay5iYXNpY0NvbXBvbmVudC5pZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXlUZXh0PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2suYmFzaWNDb21wb25lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAudGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcm93QTogTWFpbnRlbmFuY2VSb3dUeXBlcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICByb3dCOiBNYWludGVuYW5jZVJvd1R5cGVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93QT8ub3JpZ2luYWw/LmJhc2ljQ29tcG9uZW50Py50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd0I/Lm9yaWdpbmFsPy5iYXNpY0NvbXBvbmVudD8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICBdKSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnYXNzaWduZWQnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIkFzc2lnbmVkXCIgLz5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAndGFibGV0LXNtJyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBNYWludGVuYW5jZVJvd1R5cGVzIH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1haW50ZW5hbmNlQ2hlY2sgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNyZXdEZXRhaWxzID0gZ2V0Q3Jld0RldGFpbHMoXHJcbiAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay5hc3NpZ25lZFRvPy5pZCB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgIGNyZXdJbmZvLFxyXG4gICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge21haW50ZW5hbmNlQ2hlY2suYXNzaWduZWRUbz8uaWQgPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIuNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXIgY2xhc3NOYW1lPVwiaC04IHctOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENyZXdJbml0aWFscyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3RGV0YWlscz8uZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdEZXRhaWxzPy5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL2NyZXcvaW5mbz9pZD0ke21haW50ZW5hbmNlQ2hlY2suYXNzaWduZWRUby5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LWN1cmlvdXMtYmx1ZS00MDAgaGlkZGVuIGxnOmJsb2NrXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLmFzc2lnbmVkVG8ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAoXHJcbiAgICAgICAgICAgICAgICByb3dBOiBNYWludGVuYW5jZVJvd1R5cGVzLFxyXG4gICAgICAgICAgICAgICAgcm93QjogTWFpbnRlbmFuY2VSb3dUeXBlcyxcclxuICAgICAgICAgICAgKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8uYXNzaWduZWRUbz8ubmFtZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID0gcm93Qj8ub3JpZ2luYWw/LmFzc2lnbmVkVG8/Lm5hbWUgfHwgJydcclxuICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnaW52ZW50b3J5JyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnSW52ZW50b3J5JyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAndGFibGV0LWxnJyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBNYWludGVuYW5jZVJvd1R5cGVzIH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1haW50ZW5hbmNlQ2hlY2sgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge21haW50ZW5hbmNlQ2hlY2suaW52ZW50b3J5Py5pZCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL2ludmVudG9yeS92aWV3P2lkPSR7bWFpbnRlbmFuY2VDaGVjay5pbnZlbnRvcnkuaWR9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LWN1cmlvdXMtYmx1ZS00MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bWFpbnRlbmFuY2VDaGVjay5pbnZlbnRvcnkuaXRlbX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdzdGF0dXMnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIlN0YXR1c1wiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdyaWdodCcgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogTWFpbnRlbmFuY2VSb3dUeXBlcyB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBtYWludGVuYW5jZUNoZWNrID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmJsb2NrXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTdGF0dXNCYWRnZSBtYWludGVuYW5jZUNoZWNrPXttYWludGVuYW5jZUNoZWNrfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChcclxuICAgICAgICAgICAgICAgIHJvd0E6IE1haW50ZW5hbmNlUm93VHlwZXMsXHJcbiAgICAgICAgICAgICAgICByb3dCOiBNYWludGVuYW5jZVJvd1R5cGVzLFxyXG4gICAgICAgICAgICApID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IHJvd0E/Lm9yaWdpbmFsPy5pc092ZXJEdWU/LmRheXMgfHwgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy5pc092ZXJEdWU/LmRheXMgfHwgJydcclxuICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgIF0pXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8RGF0YVRhYmxlXHJcbiAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgIGRhdGE9e21haW50ZW5hbmNlQ2hlY2tzfVxyXG4gICAgICAgICAgICBwYWdlU2l6ZT17MjB9XHJcbiAgICAgICAgICAgIHNob3dUb29sYmFyPXtmYWxzZX1cclxuICAgICAgICAvPlxyXG4gICAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUYXNrTGlzdCgpIHtcclxuICAgIGNvbnN0IFttYWludGVuYW5jZUNoZWNrcywgc2V0TWFpbnRlbmFuY2VDaGVja3NdID1cclxuICAgICAgICB1c2VTdGF0ZTxNYWludGVuYW5jZUNoZWNrW10+KClcclxuICAgIGNvbnN0IFtmaWx0ZXJlZE1haW50ZW5hbmNlQ2hlY2tzLCBzZXRGaWx0ZXJlZE1haW50ZW5hbmNlQ2hlY2tzXSA9XHJcbiAgICAgICAgdXNlU3RhdGU8TWFpbnRlbmFuY2VDaGVja1tdPigpXHJcbiAgICBjb25zdCBbdmVzc2Vscywgc2V0VmVzc2Vsc10gPSB1c2VTdGF0ZTxWZXNzZWxbXT4oKVxyXG4gICAgY29uc3QgW2NyZXdJbmZvLCBzZXRDcmV3SW5mb10gPSB1c2VTdGF0ZTxDcmV3TWVtYmVyW10+KClcclxuICAgIGNvbnN0IFtmaWx0ZXIsIHNldEZpbHRlcl0gPSB1c2VTdGF0ZSh7fSBhcyBTZWFyY2hGaWx0ZXIpXHJcbiAgICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcclxuICAgIGNvbnN0IFtrZXl3b3JkRmlsdGVyLCBzZXRLZXl3b3JkRmlsdGVyXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXHJcbiAgICBjb25zdCBbcGVybWlzc2lvbnMsIHNldFBlcm1pc3Npb25zXSA9IHVzZVN0YXRlPGFueT4oZmFsc2UpXHJcbiAgICBjb25zdCBbZWRpdF90YXNrLCBzZXRFZGl0X3Rhc2tdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpXHJcbiAgICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKClcclxuICAgIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpXHJcbiAgICBjb25zdCB7IGdldFZlc3NlbFdpdGhJY29uIH0gPSB1c2VWZXNzZWxJY29uRGF0YSgpXHJcbiAgICBjb25zdCBicCA9IHVzZUJyZWFrcG9pbnRzKClcclxuICAgIGNvbnN0IFtmaWx0ZXJJc092ZXJEdWUsIHNldEZpbHRlcklzT3ZlckR1ZV0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IGluaXRfcGVybWlzc2lvbnMgPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKHBlcm1pc3Npb25zKSB7XHJcbiAgICAgICAgICAgIGlmIChoYXNQZXJtaXNzaW9uKCdFRElUX1RBU0snLCBwZXJtaXNzaW9ucykpIHtcclxuICAgICAgICAgICAgICAgIHNldEVkaXRfdGFzayh0cnVlKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgc2V0RWRpdF90YXNrKGZhbHNlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgc2V0UGVybWlzc2lvbnMoZ2V0UGVybWlzc2lvbnMpXHJcbiAgICAgICAgaW5pdF9wZXJtaXNzaW9ucygpXHJcbiAgICB9LCBbXSlcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChwZXJtaXNzaW9ucykge1xyXG4gICAgICAgICAgICBpbml0X3Blcm1pc3Npb25zKClcclxuICAgICAgICB9XHJcbiAgICB9LCBbcGVybWlzc2lvbnNdKVxyXG5cclxuICAgIGNvbnN0IFtxdWVyeU1haW50ZW5hbmNlQ2hlY2tzXSA9IHVzZUxhenlRdWVyeShcclxuICAgICAgICBSZWFkQ29tcG9uZW50TWFpbnRlbmFuY2VDaGVja0xpc3QsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZENvbXBvbmVudE1haW50ZW5hbmNlQ2hlY2tMaXN0WzBdLm5vZGVzXHJcbiAgICAgICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChmaWx0ZXJJc092ZXJEdWUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNPdmVyRHVlID0gZGF0YS5maWx0ZXIoKHRhc2s6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRhc2suaXNPdmVyRHVlLmRheSA8IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2V0TWFpbnRlbmFuY2VDaGVja3MoaXNPdmVyRHVlKVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVNldE1haW50ZW5hbmNlQ2hlY2tzKGRhdGEpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigncXVlcnlNYWludGVuYW5jZUNoZWNrcyBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICApXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChpc0xvYWRpbmcpIHtcclxuICAgICAgICAgICAgbG9hZE1haW50ZW5hbmNlQ2hlY2tzKClcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0xvYWRpbmddKVxyXG4gICAgY29uc3QgbG9hZE1haW50ZW5hbmNlQ2hlY2tzID0gYXN5bmMgKGZpbHRlcjogYW55ID0ge30pID0+IHtcclxuICAgICAgICBhd2FpdCBxdWVyeU1haW50ZW5hbmNlQ2hlY2tzKHtcclxuICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICBpbnZlbnRvcnlJRDogMCxcclxuICAgICAgICAgICAgICAgIHZlc3NlbElEOiAwLFxyXG4gICAgICAgICAgICAgICAgYXJjaGl2ZWQ6IHNlYXJjaFBhcmFtcy5nZXQoJ2FyY2hpdmVkJykgPT09ICd0cnVlJyA/IDEgOiAwLFxyXG4gICAgICAgICAgICAgICAgZmlsdGVyOiBmaWx0ZXIsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuICAgIGNvbnN0IGhhbmRsZVNldFZlc3NlbHMgPSAodmVzc2VsczogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc3QgYWN0aXZlVmVzc2VscyA9IHZlc3NlbHMuZmlsdGVyKCh2ZXNzZWw6IGFueSkgPT4gIXZlc3NlbC5hcmNoaXZlZClcclxuICAgICAgICBjb25zdCBhcHBlbmRlZERhdGEgPSBhY3RpdmVWZXNzZWxzLm1hcCgoaXRlbTogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICAuLi5pdGVtLFxyXG4gICAgICAgIH0pKVxyXG4gICAgICAgIGFwcGVuZGVkRGF0YS5wdXNoKHsgdGl0bGU6ICdPdGhlcicsIGlkOiAwIH0pXHJcbiAgICAgICAgc2V0VmVzc2VscyhhcHBlbmRlZERhdGEpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZ2V0VmVzc2VsTGlzdCA9IChoYW5kbGVTZXRWZXNzZWxzOiBhbnksIG9mZmxpbmU6IGJvb2xlYW4gPSBmYWxzZSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxyXG4gICAgICAgIGNvbnN0IHZlc3NlbE1vZGVsID0gbmV3IFZlc3NlbE1vZGVsKClcclxuICAgICAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoaXNMb2FkaW5nKSB7XHJcbiAgICAgICAgICAgICAgICBsb2FkVmVzc2VscygpXHJcbiAgICAgICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LCBbaXNMb2FkaW5nXSlcclxuXHJcbiAgICAgICAgY29uc3QgW3F1ZXJ5VmVzc2Vsc10gPSB1c2VMYXp5UXVlcnkoUmVhZFZlc3NlbHMsIHtcclxuICAgICAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocXVlcnlWZXNzZWxSZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAocXVlcnlWZXNzZWxSZXNwb25zZS5yZWFkVmVzc2Vscy5ub2Rlcykge1xyXG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZVNldFZlc3NlbHMocXVlcnlWZXNzZWxSZXNwb25zZS5yZWFkVmVzc2Vscy5ub2RlcylcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5VmVzc2VscyBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgY29uc3QgbG9hZFZlc3NlbHMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHZlc3NlbE1vZGVsLmdldEFsbCgpXHJcbiAgICAgICAgICAgICAgICBoYW5kbGVTZXRWZXNzZWxzKHJlc3BvbnNlKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgYXdhaXQgcXVlcnlWZXNzZWxzKHtcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGltaXQ6IDIwMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAwLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgZ2V0VmVzc2VsTGlzdChoYW5kbGVTZXRWZXNzZWxzKVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNldE1haW50ZW5hbmNlQ2hlY2tzID0gKHRhc2tzOiBhbnkpID0+IHtcclxuICAgICAgICBzZXRNYWludGVuYW5jZUNoZWNrcyh0YXNrcylcclxuICAgICAgICBzZXRGaWx0ZXJlZE1haW50ZW5hbmNlQ2hlY2tzKHRhc2tzKVxyXG4gICAgICAgIGNvbnN0IGFwcGVuZGVkRGF0YTogbnVtYmVyW10gPSBBcnJheS5mcm9tKFxyXG4gICAgICAgICAgICBuZXcgU2V0KFxyXG4gICAgICAgICAgICAgICAgdGFza3NcclxuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKChpdGVtOiBhbnkpID0+IGl0ZW0uYXNzaWduZWRUby5pZCA+IDApXHJcbiAgICAgICAgICAgICAgICAgICAgLm1hcCgoaXRlbTogYW55KSA9PiBpdGVtLmFzc2lnbmVkVG8uaWQpLFxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgIClcclxuICAgICAgICBsb2FkQ3Jld01lbWJlckluZm8oYXBwZW5kZWREYXRhKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IFtxdWVyeUNyZXdNZW1iZXJJbmZvXSA9IHVzZUxhenlRdWVyeShSZWFkU2VhTG9nc01lbWJlcnMsIHtcclxuICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJlYWRTZWFMb2dzTWVtYmVycy5ub2Rlc1xyXG4gICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgc2V0Q3Jld0luZm8oZGF0YSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5Q3Jld01lbWJlckluZm8gZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IGxvYWRDcmV3TWVtYmVySW5mbyA9IGFzeW5jIChjcmV3SWQ6IGFueSkgPT4ge1xyXG4gICAgICAgIGF3YWl0IHF1ZXJ5Q3Jld01lbWJlckluZm8oe1xyXG4gICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgIGNyZXdNZW1iZXJJRHM6IGNyZXdJZC5sZW5ndGggPiAwID8gY3Jld0lkIDogWzBdLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcbiAgICBjb25zdCBoYW5kbGVGaWx0ZXJPbkNoYW5nZSA9ICh7IHR5cGUsIGRhdGEgfTogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc3Qgc2VhcmNoRmlsdGVyOiBTZWFyY2hGaWx0ZXIgPSB7IC4uLmZpbHRlciB9XHJcbiAgICAgICAgLy8gbGV0IGZpbHRlcmVkVGFza3MgPSBtYWludGVuYW5jZUNoZWNrcyB8fCBbXVxyXG4gICAgICAgIHNldEZpbHRlcklzT3ZlckR1ZShmYWxzZSlcclxuICAgICAgICAvLyBWZXNzZWwgZmlsdGVyXHJcbiAgICAgICAgaWYgKHR5cGUgPT09ICd2ZXNzZWwnKSB7XHJcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGEpICYmIGRhdGEubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgICAgc2VhcmNoRmlsdGVyLmJhc2ljQ29tcG9uZW50SUQgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaW46IGRhdGEubWFwKChpdGVtKSA9PiAraXRlbS52YWx1ZSksXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YSAmJiAhQXJyYXkuaXNBcnJheShkYXRhKSkge1xyXG4gICAgICAgICAgICAgICAgc2VhcmNoRmlsdGVyLmJhc2ljQ29tcG9uZW50SUQgPSB7IGVxOiArZGF0YS52YWx1ZSB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBkZWxldGUgc2VhcmNoRmlsdGVyLmJhc2ljQ29tcG9uZW50SURcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gU3RhdHVzIGZpbHRlclxyXG4gICAgICAgIGlmICh0eXBlID09PSAnc3RhdHVzJykge1xyXG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkYXRhKSAmJiBkYXRhLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5zdGF0dXMgPSB7IGluOiBkYXRhLm1hcCgoaXRlbSkgPT4gaXRlbS52YWx1ZSkgfVxyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEgJiYgIUFycmF5LmlzQXJyYXkoZGF0YSkpIHtcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhLnZhbHVlICE9PSAnT3ZlcmR1ZScpIHtcclxuICAgICAgICAgICAgICAgICAgICBzZWFyY2hGaWx0ZXIuc3RhdHVzID0geyBlcTogZGF0YS52YWx1ZSB9XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBzZWFyY2hGaWx0ZXIuc3RhdHVzXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0RmlsdGVySXNPdmVyRHVlKHRydWUpXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBkZWxldGUgc2VhcmNoRmlsdGVyLnN0YXR1c1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBBc3NpZ25lZCBtZW1iZXIgZmlsdGVyXHJcbiAgICAgICAgaWYgKHR5cGUgPT09ICdtZW1iZXInKSB7XHJcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGEpICYmIGRhdGEubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgICAgc2VhcmNoRmlsdGVyLmFzc2lnbmVkVG9JRCA9IHtcclxuICAgICAgICAgICAgICAgICAgICBpbjogZGF0YS5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChpdGVtKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKyh0eXBlb2YgaXRlbSA9PT0gJ29iamVjdCcgPyBpdGVtLnZhbHVlIDogaXRlbSksXHJcbiAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhICYmICFBcnJheS5pc0FycmF5KGRhdGEpKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgYm90aCBvYmplY3QgZm9ybWF0IHt2YWx1ZTogaWR9IGFuZCBkaXJlY3QgSUQgdmFsdWVcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1lbWJlcklkID0gdHlwZW9mIGRhdGEgPT09ICdvYmplY3QnID8gZGF0YS52YWx1ZSA6IGRhdGFcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5hc3NpZ25lZFRvSUQgPSB7IGVxOiArbWVtYmVySWQgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgZGVsZXRlIHNlYXJjaEZpbHRlci5hc3NpZ25lZFRvSURcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gRGF0ZSByYW5nZVxyXG4gICAgICAgIGlmICh0eXBlID09PSAnZGF0ZVJhbmdlJykge1xyXG4gICAgICAgICAgICBpZiAoZGF0YT8uc3RhcnREYXRlICYmIGRhdGE/LmVuZERhdGUpIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5leHBpcmVzID0ge1xyXG4gICAgICAgICAgICAgICAgICAgIGd0ZTogZGF5anMoZGF0YS5zdGFydERhdGUpLmZvcm1hdCgnWVlZWS1NTS1ERCAwMDowMDowMCcpLFxyXG4gICAgICAgICAgICAgICAgICAgIGx0ZTogZGF5anMoZGF0YS5lbmREYXRlKS5mb3JtYXQoJ1lZWVktTU0tREQgMjM6NTk6NTknKSxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSBzZWFyY2hGaWx0ZXIuZXhwaXJlc1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBDYXRlZ29yeVxyXG4gICAgICAgIGlmICh0eXBlID09PSAnY2F0ZWdvcnknKSB7XHJcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGEpICYmIGRhdGEubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgICAgc2VhcmNoRmlsdGVyLm1haW50ZW5hbmNlQ2F0ZWdvcnlJRCA9IHtcclxuICAgICAgICAgICAgICAgICAgICBpbjogZGF0YS5tYXAoKGl0ZW0pID0+ICtpdGVtLnZhbHVlKSxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhICYmICFBcnJheS5pc0FycmF5KGRhdGEpKSB7XHJcbiAgICAgICAgICAgICAgICBzZWFyY2hGaWx0ZXIubWFpbnRlbmFuY2VDYXRlZ29yeUlEID0geyBlcTogK2RhdGEudmFsdWUgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgZGVsZXRlIHNlYXJjaEZpbHRlci5tYWludGVuYW5jZUNhdGVnb3J5SURcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gUmVjdXJyaW5nIGZpbHRlciAtIGhhbmRsZSBjbGllbnQtc2lkZSBmaWx0ZXJpbmdcclxuICAgICAgICAvLyBsZXQgcmVjdXJyaW5nRmlsdGVyID0gbnVsbFxyXG5cclxuICAgICAgICBpZiAodHlwZSA9PT0gJ3JlY3VycmluZycpIHtcclxuICAgICAgICAgICAgaWYgKGRhdGEgJiYgIUFycmF5LmlzQXJyYXkoZGF0YSkpIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci50YXNrVHlwZSA9IHsgZXE6IGRhdGEudmFsdWUgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgZGVsZXRlIHNlYXJjaEZpbHRlci50YXNrVHlwZVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyAvLyBLZXl3b3JkIGZpbHRlclxyXG4gICAgICAgIGxldCBrZXlGaWx0ZXIgPSBrZXl3b3JkRmlsdGVyXHJcbiAgICAgICAgaWYgKHR5cGUgPT09ICdrZXl3b3JkJykge1xyXG4gICAgICAgICAgICBpZiAoIWlzRW1wdHkodHJpbShkYXRhPy52YWx1ZSkpKSB7XHJcbiAgICAgICAgICAgICAgICBzZWFyY2hGaWx0ZXIubmFtZSA9IHsgY29udGFpbnM6IGRhdGE/LnZhbHVlIH1cclxuICAgICAgICAgICAgICAgIGtleUZpbHRlciA9IGRhdGE/LnZhbHVlXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBkZWxldGUgc2VhcmNoRmlsdGVyLm5hbWVcclxuICAgICAgICAgICAgICAgIGtleUZpbHRlciA9IG51bGxcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSBpZiAoIWlzRW1wdHkoa2V5RmlsdGVyKSkge1xyXG4gICAgICAgICAgICBzZWFyY2hGaWx0ZXIubmFtZSA9IHsgY29udGFpbnM6IGtleUZpbHRlciA/PyAnJyB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgZGVsZXRlIHNlYXJjaEZpbHRlci5uYW1lXHJcbiAgICAgICAgICAgIGtleUZpbHRlciA9IG51bGxcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gaWYgKHR5cGUgPT09ICdrZXl3b3JkJyB8fCAoa2V5RmlsdGVyICYmIGtleUZpbHRlci5sZW5ndGggPiAwKSkge1xyXG4gICAgICAgIC8vICAgICBjb25zdCBrZXl3b3JkID0gZGF0YT8udmFsdWU/LnRyaW0oKS50b0xvd2VyQ2FzZSgpXHJcbiAgICAgICAgLy8gICAgIGlmIChrZXl3b3JkICYmIGtleXdvcmQubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIC8vICAgICAgICAgZmlsdGVyZWRUYXNrcyA9IGZpbHRlcmVkVGFza3MuZmlsdGVyKFxyXG4gICAgICAgIC8vICAgICAgICAgICAgIChtYWludGVuYW5jZUNoZWNrOiBNYWludGVuYW5jZUNoZWNrKSA9PlxyXG4gICAgICAgIC8vICAgICAgICAgICAgICAgICBbXHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLm5hbWUsXHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLmNvbW1lbnRzLFxyXG4gICAgICAgIC8vICAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay53b3JrT3JkZXJOdW1iZXIsXHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgIF0uc29tZSgoZmllbGQpID0+XHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgICAgICBmaWVsZD8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXl3b3JkKSxcclxuICAgICAgICAvLyAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAvLyAgICAgICAgIClcclxuICAgICAgICAvLyAgICAgICAgIGtleUZpbHRlciA9IGRhdGEudmFsdWVcclxuICAgICAgICAvLyAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyAgICAgICAgIGtleUZpbHRlciA9IG51bGxcclxuICAgICAgICAvLyAgICAgfVxyXG4gICAgICAgIC8vIH1cclxuXHJcbiAgICAgICAgLy8gRmlsdGVyaW5nIGJhc2VkIG9uIGN1cnJlbnQgc2VhcmNoRmlsdGVyXHJcblxyXG4gICAgICAgIC8vIC8vIEZpbHRlciBieSB2ZXNzZWwgKGJhc2ljQ29tcG9uZW50SUQpXHJcbiAgICAgICAgLy8gaWYgKHNlYXJjaEZpbHRlci5iYXNpY0NvbXBvbmVudElEKSB7XHJcbiAgICAgICAgLy8gICAgIGNvbnN0IGlkcyA9IHNlYXJjaEZpbHRlci5iYXNpY0NvbXBvbmVudElELmluIHx8IFtcclxuICAgICAgICAvLyAgICAgICAgIHNlYXJjaEZpbHRlci5iYXNpY0NvbXBvbmVudElELmVxLFxyXG4gICAgICAgIC8vICAgICBdXHJcbiAgICAgICAgLy8gICAgIGZpbHRlcmVkVGFza3MgPSBmaWx0ZXJlZFRhc2tzLmZpbHRlcigobWM6IE1haW50ZW5hbmNlQ2hlY2spID0+XHJcbiAgICAgICAgLy8gICAgICAgICBpZHMuaW5jbHVkZXMobWMuYmFzaWNDb21wb25lbnQ/LmlkKSxcclxuICAgICAgICAvLyAgICAgKVxyXG4gICAgICAgIC8vIH1cclxuXHJcbiAgICAgICAgLy8gRmlsdGVyIGJ5IHN0YXR1c1xyXG4gICAgICAgIC8vIGlmIChzZWFyY2hGaWx0ZXIuc3RhdHVzKSB7XHJcbiAgICAgICAgLy8gICAgIGNvbnN0IHN0YXR1c2VzID0gc2VhcmNoRmlsdGVyLnN0YXR1cy5pbiB8fCBbc2VhcmNoRmlsdGVyLnN0YXR1cy5lcV1cclxuICAgICAgICAvLyAgICAgZmlsdGVyZWRUYXNrcyA9IGZpbHRlcmVkVGFza3MuZmlsdGVyKChtYzogTWFpbnRlbmFuY2VDaGVjaykgPT5cclxuICAgICAgICAvLyAgICAgICAgIHN0YXR1c2VzLmluY2x1ZGVzKG1jLnN0YXR1cyksXHJcbiAgICAgICAgLy8gICAgIClcclxuICAgICAgICAvLyB9XHJcblxyXG4gICAgICAgIC8vIC8vIEZpbHRlciBieSBhc3NpZ25lZFRvSURcclxuICAgICAgICAvLyBpZiAoc2VhcmNoRmlsdGVyLmFzc2lnbmVkVG9JRCkge1xyXG4gICAgICAgIC8vICAgICBjb25zdCBpZHMgPSBzZWFyY2hGaWx0ZXIuYXNzaWduZWRUb0lELmluIHx8IFtcclxuICAgICAgICAvLyAgICAgICAgIHNlYXJjaEZpbHRlci5hc3NpZ25lZFRvSUQuZXEsXHJcbiAgICAgICAgLy8gICAgIF1cclxuICAgICAgICAvLyAgICAgZmlsdGVyZWRUYXNrcyA9IGZpbHRlcmVkVGFza3MuZmlsdGVyKChtYzogTWFpbnRlbmFuY2VDaGVjaykgPT5cclxuICAgICAgICAvLyAgICAgICAgIGlkcy5pbmNsdWRlcyhtYy5hc3NpZ25lZFRvPy5pZCksXHJcbiAgICAgICAgLy8gICAgIClcclxuICAgICAgICAvLyB9XHJcblxyXG4gICAgICAgIC8vIEZpbHRlciBieSBjYXRlZ29yeVxyXG4gICAgICAgIC8vIGlmIChzZWFyY2hGaWx0ZXIubWFpbnRlbmFuY2VDYXRlZ29yeUlEKSB7XHJcbiAgICAgICAgLy8gICAgIGNvbnN0IGlkcyA9IHNlYXJjaEZpbHRlci5tYWludGVuYW5jZUNhdGVnb3J5SUQuaW4gfHwgW1xyXG4gICAgICAgIC8vICAgICAgICAgc2VhcmNoRmlsdGVyLm1haW50ZW5hbmNlQ2F0ZWdvcnlJRC5lcSxcclxuICAgICAgICAvLyAgICAgXVxyXG4gICAgICAgIC8vICAgICBmaWx0ZXJlZFRhc2tzID0gZmlsdGVyZWRUYXNrcy5maWx0ZXIoKG1jOiBNYWludGVuYW5jZUNoZWNrKSA9PlxyXG4gICAgICAgIC8vICAgICAgICAgaWRzLmluY2x1ZGVzKG1jLm1haW50ZW5hbmNlQ2F0ZWdvcnlJRCksXHJcbiAgICAgICAgLy8gICAgIClcclxuICAgICAgICAvLyB9XHJcblxyXG4gICAgICAgIC8vIC8vIEZpbHRlciBieSBkYXRlIHJhbmdlXHJcbiAgICAgICAgLy8gaWYgKFxyXG4gICAgICAgIC8vICAgICBzZWFyY2hGaWx0ZXIuZXhwaXJlcyAmJlxyXG4gICAgICAgIC8vICAgICBzZWFyY2hGaWx0ZXIuZXhwaXJlcy5ndGUgJiZcclxuICAgICAgICAvLyAgICAgc2VhcmNoRmlsdGVyLmV4cGlyZXMubHRlXHJcbiAgICAgICAgLy8gKSB7XHJcbiAgICAgICAgLy8gICAgIGZpbHRlcmVkVGFza3MgPSBmaWx0ZXJlZFRhc2tzLmZpbHRlcihcclxuICAgICAgICAvLyAgICAgICAgIChtYzogTWFpbnRlbmFuY2VDaGVjaykgPT5cclxuICAgICAgICAvLyAgICAgICAgICAgICBkYXlqcyhtYy5zdGFydERhdGUpLmlzQWZ0ZXIoXHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgIGRheWpzKHNlYXJjaEZpbHRlci5leHBpcmVzIS5ndGUpLFxyXG4gICAgICAgIC8vICAgICAgICAgICAgICkgJiZcclxuICAgICAgICAvLyAgICAgICAgICAgICBkYXlqcyhtYy5zdGFydERhdGUpLmlzQmVmb3JlKFxyXG4gICAgICAgIC8vICAgICAgICAgICAgICAgICBkYXlqcyhzZWFyY2hGaWx0ZXIuZXhwaXJlcyEubHRlKSxcclxuICAgICAgICAvLyAgICAgICAgICAgICApLFxyXG4gICAgICAgIC8vICAgICApXHJcbiAgICAgICAgLy8gfVxyXG5cclxuICAgICAgICAvLyBGaWx0ZXIgYnkgcmVjdXJyaW5nIHN0YXR1c1xyXG4gICAgICAgIC8vIGlmIChyZWN1cnJpbmdGaWx0ZXIpIHtcclxuICAgICAgICAvLyAgICAgaWYgKHJlY3VycmluZ0ZpbHRlciA9PT0gJ3JlY3VycmluZycpIHtcclxuICAgICAgICAvLyAgICAgICAgIC8vIFJlY3VycmluZyB0YXNrcyBoYXZlIHJlY3VycmluZ0lEID4gMFxyXG4gICAgICAgIC8vICAgICAgICAgZmlsdGVyZWRUYXNrcyA9IGZpbHRlcmVkVGFza3MuZmlsdGVyKFxyXG4gICAgICAgIC8vICAgICAgICAgICAgIChtYzogTWFpbnRlbmFuY2VDaGVjaykgPT4gbWMucmVjdXJyaW5nSUQgPiAwLFxyXG4gICAgICAgIC8vICAgICAgICAgKVxyXG4gICAgICAgIC8vICAgICB9IGVsc2UgaWYgKHJlY3VycmluZ0ZpbHRlciA9PT0gJ29uZS1vZmYnKSB7XHJcbiAgICAgICAgLy8gICAgICAgICAvLyBPbmUtb2ZmIHRhc2tzIGhhdmUgcmVjdXJyaW5nSUQgPSAwIG9yIG51bGxcclxuICAgICAgICAvLyAgICAgICAgIGZpbHRlcmVkVGFza3MgPSBmaWx0ZXJlZFRhc2tzLmZpbHRlcihcclxuICAgICAgICAvLyAgICAgICAgICAgICAobWM6IE1haW50ZW5hbmNlQ2hlY2spID0+XHJcbiAgICAgICAgLy8gICAgICAgICAgICAgICAgICFtYy5yZWN1cnJpbmdJRCB8fCBtYy5yZWN1cnJpbmdJRCA9PT0gMCxcclxuICAgICAgICAvLyAgICAgICAgIClcclxuICAgICAgICAvLyAgICAgfVxyXG4gICAgICAgIC8vIH1cclxuXHJcbiAgICAgICAgLy8gU2V0IHVwZGF0ZWQgZmlsdGVyc1xyXG4gICAgICAgIHNldEZpbHRlcihzZWFyY2hGaWx0ZXIpXHJcbiAgICAgICAgc2V0S2V5d29yZEZpbHRlcihrZXlGaWx0ZXIpXHJcbiAgICAgICAgLy8gc2V0RmlsdGVyZWRNYWludGVuYW5jZUNoZWNrcyhmaWx0ZXJlZFRhc2tzKVxyXG4gICAgICAgIGxvYWRNYWludGVuYW5jZUNoZWNrcyhzZWFyY2hGaWx0ZXIpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZG93bmxvYWRDc3YgPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKCFtYWludGVuYW5jZUNoZWNrcyB8fCAhdmVzc2Vscykge1xyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGNzdkVudHJpZXM6IHN0cmluZ1tdW10gPSBbXHJcbiAgICAgICAgICAgIFsndGFzaycsICdsb2NhdGlvbicsICdhc3NpZ25lZCB0bycsICdkdWUnXSxcclxuICAgICAgICBdXHJcblxyXG4gICAgICAgIG1haW50ZW5hbmNlQ2hlY2tzXHJcbiAgICAgICAgICAgIC5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAobWFpbnRlbmFuY2VDaGVjazogTWFpbnRlbmFuY2VDaGVjaykgPT5cclxuICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLnN0YXR1cyAhPT0gJ1NhdmVfQXNfRHJhZnQnLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIC5mb3JFYWNoKChtYWludGVuYW5jZUNoZWNrOiBNYWludGVuYW5jZUNoZWNrKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBjcmV3RGV0YWlscyA9IGdldENyZXdEZXRhaWxzKFxyXG4gICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2suYXNzaWduZWRUbz8uaWQgfHwgMCxcclxuICAgICAgICAgICAgICAgICAgICBjcmV3SW5mbyxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIGNvbnN0IGFzc2lnbmVkVG9OYW1lID0gY3Jld0RldGFpbHNcclxuICAgICAgICAgICAgICAgICAgICA/IGAke2NyZXdEZXRhaWxzLmZpcnN0TmFtZX0gJHtjcmV3RGV0YWlscy5zdXJuYW1lfWBcclxuICAgICAgICAgICAgICAgICAgICA6IG1haW50ZW5hbmNlQ2hlY2suYXNzaWduZWRUbz8ubmFtZSB8fCAnJ1xyXG5cclxuICAgICAgICAgICAgICAgIGNzdkVudHJpZXMucHVzaChbXHJcbiAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay5uYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgIHZlc3NlbHNcclxuICAgICAgICAgICAgICAgICAgICAgICAgPy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAodmVzc2VsOiBWZXNzZWwpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsPy5pZCA9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2suYmFzaWNDb21wb25lbnQ/LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoKHZlc3NlbDogVmVzc2VsKSA9PiB2ZXNzZWwudGl0bGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5qb2luKCcsICcpIHx8ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgIGFzc2lnbmVkVG9OYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgIGR1ZVN0YXR1c0xhYmVsKG1haW50ZW5hbmNlQ2hlY2suaXNPdmVyRHVlKSxcclxuICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgIGV4cG9ydENzdihjc3ZFbnRyaWVzKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGRvd25sb2FkUGRmID0gKCkgPT4ge1xyXG4gICAgICAgIGlmICghbWFpbnRlbmFuY2VDaGVja3MgfHwgIXZlc3NlbHMpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBoZWFkZXJzOiBhbnkgPSBbWydUYXNrIE5hbWUnLCAnTG9jYXRpb24nLCAnQXNzaWduZWQgVG8nLCAnRHVlJ11dXHJcblxyXG4gICAgICAgIGNvbnN0IGJvZHk6IGFueSA9IG1haW50ZW5hbmNlQ2hlY2tzXHJcbiAgICAgICAgICAgIC5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAobWFpbnRlbmFuY2VDaGVjazogTWFpbnRlbmFuY2VDaGVjaykgPT5cclxuICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLnN0YXR1cyAhPT0gJ1NhdmVfQXNfRHJhZnQnLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICAgIC5tYXAoKG1haW50ZW5hbmNlQ2hlY2s6IE1haW50ZW5hbmNlQ2hlY2spID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNyZXdEZXRhaWxzID0gZ2V0Q3Jld0RldGFpbHMoXHJcbiAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay5hc3NpZ25lZFRvPy5pZCB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgIGNyZXdJbmZvLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgY29uc3QgYXNzaWduZWRUb05hbWUgPSBjcmV3RGV0YWlsc1xyXG4gICAgICAgICAgICAgICAgICAgID8gYCR7Y3Jld0RldGFpbHMuZmlyc3ROYW1lfSAke2NyZXdEZXRhaWxzLnN1cm5hbWV9YFxyXG4gICAgICAgICAgICAgICAgICAgIDogbWFpbnRlbmFuY2VDaGVjay5hc3NpZ25lZFRvPy5uYW1lIHx8ICcnXHJcblxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFtcclxuICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgdmVzc2Vsc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA/LmZpbHRlcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICh2ZXNzZWw6IFZlc3NlbCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw/LmlkID09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay5iYXNpY0NvbXBvbmVudD8uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgLm1hcCgodmVzc2VsOiBWZXNzZWwpID0+IHZlc3NlbC50aXRsZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgLmpvaW4oJywgJykgfHwgJycsXHJcbiAgICAgICAgICAgICAgICAgICAgYXNzaWduZWRUb05hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgZHVlU3RhdHVzTGFiZWwobWFpbnRlbmFuY2VDaGVjay5pc092ZXJEdWUpLFxyXG4gICAgICAgICAgICAgICAgXVxyXG4gICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICBleHBvcnRQZGZUYWJsZSh7XHJcbiAgICAgICAgICAgIGhlYWRlcnMsXHJcbiAgICAgICAgICAgIGJvZHksXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBnZXRTdGF0dXNDb2xvckNsYXNzZXMgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcclxuICAgICAgICBzd2l0Y2ggKHN0YXR1cykge1xyXG4gICAgICAgICAgICBjYXNlICdIaWdoJzpcclxuICAgICAgICAgICAgICAgIHJldHVybiAndGV4dC1kZXN0cnVjdGl2ZSBob3Zlcjp0ZXh0LWNpbm5hYmFyLTgwMCdcclxuICAgICAgICAgICAgY2FzZSAnVXBjb21pbmcnOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuICd0ZXh0LXdhcm5pbmcgaG92ZXI6dGV4dC1maXJlLWJ1c2gtNTAwJ1xyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuICdob3Zlcjp0ZXh0LWN1cmlvdXMtYmx1ZS00MDAnXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNyZWF0ZU1haW50ZW5hbmNlQ29sdW1ucyA9IChcclxuICAgICAgICBjcmV3SW5mbzogQ3Jld01lbWJlcltdLFxyXG4gICAgICAgIGdldFZlc3NlbFdpdGhJY29uOiBhbnksXHJcbiAgICApID0+XHJcbiAgICAgICAgY3JlYXRlQ29sdW1ucyhbXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndGl0bGUnLFxyXG4gICAgICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiVGl0bGVcIiAvPlxyXG4gICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogTWFpbnRlbmFuY2VSb3dUeXBlcyB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWFpbnRlbmFuY2VDaGVjazogTWFpbnRlbmFuY2VDaGVjayA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNyZXdEZXRhaWxzID0gZ2V0Q3Jld0RldGFpbHMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2suYXNzaWduZWRUbz8uaWQgfHwgMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY3Jld0luZm8sXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZlc3NlbERldGFpbHMgPSBnZXRWZXNzZWxEZXRhaWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLmJhc2ljQ29tcG9uZW50Py5pZCB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxzLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgb3ZlckR1ZVN0YXR1cyA9IG1haW50ZW5hbmNlQ2hlY2suaXNPdmVyRHVlPy5zdGF0dXNcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBvdmVyRHVlRGF5cyA9IG1haW50ZW5hbmNlQ2hlY2suaXNPdmVyRHVlPy5kYXlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdGFza0xpbmsgPSAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL21haW50ZW5hbmNlP3Rhc2tJRD0ke21haW50ZW5hbmNlQ2hlY2suaWR9JnJlZGlyZWN0X3RvPSR7cGF0aG5hbWV9PyR7c2VhcmNoUGFyYW1zLnRvU3RyaW5nKCl9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2xlYWRpbmctdGlnaHQgdHJ1bmNhdGUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldFN0YXR1c0NvbG9yQ2xhc3NlcyhvdmVyRHVlU3RhdHVzKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge21haW50ZW5hbmNlQ2hlY2submFtZSA/P1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGBUYXNrICMke21haW50ZW5hbmNlQ2hlY2suaWR9IChObyBOYW1lKSAtICR7ZGF5anMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2suY3JlYXRlZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLmZvcm1hdCgnREQvTU0vWVlZWScpfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogU2hvdyBjYXJkIGxheW91dCBvbiB4cyBkZXZpY2VzICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YWJsZXQtc206aGlkZGVuIGlubGluZS1mbGV4IG92ZXJmbG93LWF1dG8gaXRlbXMtY2VudGVyIGdhcC0xLjVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQXNzaWduZWQgVG8gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21haW50ZW5hbmNlQ2hlY2suYXNzaWduZWRUbz8uaWQgPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJoLTggdy04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDcmV3SW5pdGlhbHMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdEZXRhaWxzPy5maXJzdE5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdEZXRhaWxzPy5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFza0xpbmt9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBJbnZlbnRvcnkgSXRlbSAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21haW50ZW5hbmNlQ2hlY2suaW52ZW50b3J5Py5pZCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC9pbnZlbnRvcnkvdmlldz9pZD0ke21haW50ZW5hbmNlQ2hlY2suaW52ZW50b3J5LmlkfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtY3VyaW91cy1ibHVlLTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLmludmVudG9yeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5pdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBTaG93IG5vcm1hbCB0YWJsZSBsYXlvdXQgb24gbGFyZ2VyIGRldmljZXMgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiB0YWJsZXQtc206YmxvY2tcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0YXNrTGlua31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogTW9iaWxlOiBTaG93IGxvY2F0aW9uICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6aGlkZGVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLmJhc2ljQ29tcG9uZW50Py5pZCA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC92ZXNzZWwvaW5mbz9pZD0ke21haW50ZW5hbmNlQ2hlY2suYmFzaWNDb21wb25lbnQuaWR9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LWN1cmlvdXMtYmx1ZS00MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2suYmFzaWNDb21wb25lbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aXRsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgcm93QTogTWFpbnRlbmFuY2VSb3dUeXBlcyxcclxuICAgICAgICAgICAgICAgICAgICByb3dCOiBNYWludGVuYW5jZVJvd1R5cGVzLFxyXG4gICAgICAgICAgICAgICAgKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/Lm5hbWUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSByb3dCPy5vcmlnaW5hbD8ubmFtZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBhY2Nlc3NvcktleTogJ2xvY2F0aW9uJyxcclxuICAgICAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIkxvY2F0aW9uXCIgLz5cclxuICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnbGVmdCcsXHJcbiAgICAgICAgICAgICAgICBicmVha3BvaW50OiAnbGFwdG9wJyxcclxuICAgICAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogTWFpbnRlbmFuY2VSb3dUeXBlcyB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWFpbnRlbmFuY2VDaGVjayA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZlc3NlbERldGFpbHMgPSBnZXRWZXNzZWxEZXRhaWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLmJhc2ljQ29tcG9uZW50Py5pZCB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxzLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gR2V0IHZlc3NlbCB3aXRoIGljb24gZGF0YVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZlc3NlbFdpdGhJY29uID0gZ2V0VmVzc2VsV2l0aEljb24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2suYmFzaWNDb21wb25lbnQ/LmlkIHx8IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbERldGFpbHMsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge21haW50ZW5hbmNlQ2hlY2suYmFzaWNDb21wb25lbnQ/LmlkID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFZlc3NlbExvY2F0aW9uRGlzcGxheVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw9e3Zlc3NlbFdpdGhJY29ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxJZD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLmJhc2ljQ29tcG9uZW50LmlkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheVRleHQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay5iYXNpY0NvbXBvbmVudC50aXRsZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgcm93QTogTWFpbnRlbmFuY2VSb3dUeXBlcyxcclxuICAgICAgICAgICAgICAgICAgICByb3dCOiBNYWludGVuYW5jZVJvd1R5cGVzLFxyXG4gICAgICAgICAgICAgICAgKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/LmJhc2ljQ29tcG9uZW50Py50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy5iYXNpY0NvbXBvbmVudD8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdhc3NpZ25lZCcsXHJcbiAgICAgICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJBc3NpZ25lZFwiIC8+XHJcbiAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnLFxyXG4gICAgICAgICAgICAgICAgYnJlYWtwb2ludDogJ3RhYmxldC1sZycsXHJcbiAgICAgICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IE1haW50ZW5hbmNlUm93VHlwZXMgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1haW50ZW5hbmNlQ2hlY2sgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjcmV3RGV0YWlscyA9IGdldENyZXdEZXRhaWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLmFzc2lnbmVkVG8/LmlkIHx8IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNyZXdJbmZvLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLmFzc2lnbmVkVG8/LmlkID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMi41XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXIgY2xhc3NOYW1lPVwiaC04IHctOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cInRleHQteHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q3Jld0luaXRpYWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3RGV0YWlscz8uZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3RGV0YWlscz8uc3VybmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL2NyZXcvaW5mbz9pZD0ke21haW50ZW5hbmNlQ2hlY2suYXNzaWduZWRUby5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1jdXJpb3VzLWJsdWUtNDAwIGhpZGRlbiB0YWJsZXQtbWQ6YmxvY2tcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLmFzc2lnbmVkVG8ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHNvcnRpbmdGbjogKFxyXG4gICAgICAgICAgICAgICAgICAgIHJvd0E6IE1haW50ZW5hbmNlUm93VHlwZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgcm93QjogTWFpbnRlbmFuY2VSb3dUeXBlcyxcclxuICAgICAgICAgICAgICAgICkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IHJvd0E/Lm9yaWdpbmFsPy5hc3NpZ25lZFRvPy5uYW1lIHx8ICcnXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID0gcm93Qj8ub3JpZ2luYWw/LmFzc2lnbmVkVG8/Lm5hbWUgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdpbnZlbnRvcnknLFxyXG4gICAgICAgICAgICAgICAgaGVhZGVyOiAnSW52ZW50b3J5JyxcclxuICAgICAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICd0YWJsZXQtc20nLFxyXG4gICAgICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBNYWludGVuYW5jZVJvd1R5cGVzIH0pID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtYWludGVuYW5jZUNoZWNrID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttYWludGVuYW5jZUNoZWNrLmludmVudG9yeT8uaWQgPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2AvaW52ZW50b3J5L3ZpZXc/aWQ9JHttYWludGVuYW5jZUNoZWNrLmludmVudG9yeS5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LWN1cmlvdXMtYmx1ZS00MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21haW50ZW5hbmNlQ2hlY2suaW52ZW50b3J5Lml0ZW19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj4tPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdzdGF0dXMnLFxyXG4gICAgICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiU3RhdHVzXCIgLz5cclxuICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAncmlnaHQnLFxyXG4gICAgICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBNYWludGVuYW5jZVJvd1R5cGVzIH0pID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtYWludGVuYW5jZUNoZWNrID0gcm93Lm9yaWdpbmFsXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGlmICghbWFpbnRlbmFuY2VDaGVjaykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gPGRpdj4tPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBvdmVyRHVlU3RhdHVzID0gbWFpbnRlbmFuY2VDaGVjay5pc092ZXJEdWU/LnN0YXR1c1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG92ZXJEdWVEYXlzID0gbWFpbnRlbmFuY2VDaGVjay5pc092ZXJEdWU/LmRheVxyXG5cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge292ZXJEdWVTdGF0dXMgPT09ICdIaWdoJyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAhYnBbJ3RhYmxldC1zbSddID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2AgaXRlbXMtZW5kIHctZml0IHRleHQtc20geHM6dGV4dC1iYXNlIHB5LTAuNSBweC0xIHhzOnB4LTMgeHM6cHktMVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJHtvdmVyRHVlU3RhdHVzID09PSAnSGlnaCcgPyAnYWxlcnQgd2hpdGVzcGFjZS1ub3dyYXAnIDogJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtvdmVyRHVlRGF5cyAqIC0xICsgJyBkYXlzIGFnbyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTdGF0dXNCYWRnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjaz17bWFpbnRlbmFuY2VDaGVja31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTdGF0dXNCYWRnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrPXttYWludGVuYW5jZUNoZWNrfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgc29ydGluZ0ZuOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgcm93QTogTWFpbnRlbmFuY2VSb3dUeXBlcyxcclxuICAgICAgICAgICAgICAgICAgICByb3dCOiBNYWludGVuYW5jZVJvd1R5cGVzLFxyXG4gICAgICAgICAgICAgICAgKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/LmlzT3ZlckR1ZT8uZGF5cyB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy5pc092ZXJEdWU/LmRheXMgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICBdKVxyXG5cclxuICAgIGNvbnN0IGNvbHVtbnMgPSBjcmVhdGVNYWludGVuYW5jZUNvbHVtbnMoY3Jld0luZm8gfHwgW10sIGdldFZlc3NlbFdpdGhJY29uKVxyXG5cclxuICAgIC8vIFJvdyBzdGF0dXMgZXZhbHVhdG9yIGZvciBtYWludGVuYW5jZSB0YXNrc1xyXG4gICAgY29uc3QgZ2V0TWFpbnRlbmFuY2VSb3dTdGF0dXM6IFJvd1N0YXR1c0V2YWx1YXRvcjxhbnk+ID0gKFxyXG4gICAgICAgIG1haW50ZW5hbmNlQ2hlY2ssXHJcbiAgICApID0+IHtcclxuICAgICAgICAvLyBTa2lwIGNvbXBsZXRlZCwgYXJjaGl2ZWQsIG9yIGRyYWZ0IHRhc2tzXHJcbiAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLnN0YXR1cyA9PT0gJ0NvbXBsZXRlZCcgfHxcclxuICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVjay5hcmNoaXZlZCB8fFxyXG4gICAgICAgICAgICBtYWludGVuYW5jZUNoZWNrLnN0YXR1cyA9PT0gJ1NhdmVfQXNfRHJhZnQnXHJcbiAgICAgICAgKSB7XHJcbiAgICAgICAgICAgIHJldHVybiAnbm9ybWFsJ1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3Qgb3ZlckR1ZVN0YXR1cyA9IG1haW50ZW5hbmNlQ2hlY2suaXNPdmVyRHVlPy5zdGF0dXNcclxuXHJcbiAgICAgICAgLy8gVXNlIHRoZSBwcmUtY2FsY3VsYXRlZCBzdGF0dXMgdmFsdWVzIGZyb20gdGhlIHN5c3RlbVxyXG4gICAgICAgIHN3aXRjaCAob3ZlckR1ZVN0YXR1cykge1xyXG4gICAgICAgICAgICBjYXNlICdIaWdoJzpcclxuICAgICAgICAgICAgICAgIHJldHVybiAnb3ZlcmR1ZScgLy8gUmVkIGhpZ2hsaWdodGluZ1xyXG4gICAgICAgICAgICBjYXNlICdVcGNvbWluZyc6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gJ3VwY29taW5nJyAvLyBPcmFuZ2UgaGlnaGxpZ2h0aW5nXHJcbiAgICAgICAgICAgIGNhc2UgJ01lZGl1bSc6XHJcbiAgICAgICAgICAgIGNhc2UgJ09wZW4nOlxyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuICdub3JtYWwnIC8vIE5vIGhpZ2hsaWdodGluZ1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDw+XHJcbiAgICAgICAgICAgIDxMaXN0SGVhZGVyXHJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIk1haW50ZW5hbmNlXCJcclxuICAgICAgICAgICAgICAgIGljb249e1xyXG4gICAgICAgICAgICAgICAgICAgIDxTZWFsb2dzTWFpbnRlbmFuY2VJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiByaW5nLTEgcC0xIHJvdW5kZWQtZnVsbCBiZy1bI2ZmZl1cIiAvPlxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgYWN0aW9ucz17PE1haW50ZW5hbmNlRmlsdGVyQWN0aW9ucyAvPn1cclxuICAgICAgICAgICAgICAgIHRpdGxlQ2xhc3NOYW1lPVwiXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xNlwiPlxyXG4gICAgICAgICAgICAgICAge21haW50ZW5hbmNlQ2hlY2tzICYmIHZlc3NlbHMgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPERhdGFUYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhPXsoZmlsdGVyZWRNYWludGVuYW5jZUNoZWNrcyBhcyBhbnkpIHx8IFtdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZT17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWx0ZXJPbkNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgcm93U3RhdHVzPXtnZXRNYWludGVuYW5jZVJvd1N0YXR1c31cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICA8VGFibGVTa2VsZXRvbiAvPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC8+XHJcbiAgICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VMYXp5UXVlcnkiLCJUYWJsZVNrZWxldG9uIiwiTGluayIsImRheWpzIiwiaXNFbXB0eSIsInRyaW0iLCJ1c2VQYXRobmFtZSIsInVzZVNlYXJjaFBhcmFtcyIsImdldFBlcm1pc3Npb25zIiwiaGFzUGVybWlzc2lvbiIsImR1ZVN0YXR1c0xhYmVsIiwiZXhwb3J0Q3N2IiwiZXhwb3J0UGRmVGFibGUiLCJEYXRhVGFibGUiLCJjcmVhdGVDb2x1bW5zIiwiRGF0YVRhYmxlU29ydEhlYWRlciIsIk1haW50ZW5hbmNlRmlsdGVyQWN0aW9ucyIsIkxpc3RIZWFkZXIiLCJBdmF0YXIiLCJBdmF0YXJGYWxsYmFjayIsIlZlc3NlbExvY2F0aW9uRGlzcGxheSIsInVzZVZlc3NlbEljb25EYXRhIiwidXNlQnJlYWtwb2ludHMiLCJTZWFsb2dzTWFpbnRlbmFuY2VJY29uIiwiUmVhZENvbXBvbmVudE1haW50ZW5hbmNlQ2hlY2tMaXN0IiwiUmVhZFNlYUxvZ3NNZW1iZXJzIiwiUmVhZFZlc3NlbHMiLCJWZXNzZWxNb2RlbCIsImNuIiwiZ2V0Q3Jld0luaXRpYWxzIiwiZmlyc3ROYW1lIiwic3VybmFtZSIsImZpcnN0IiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJsYXN0IiwiZ2V0VmVzc2VsSW5pdGlhbHMiLCJ0aXRsZSIsIndvcmRzIiwic3BsaXQiLCJmaWx0ZXIiLCJ3b3JkIiwibGVuZ3RoIiwic3Vic3RyaW5nIiwic2xpY2UiLCJtYXAiLCJqb2luIiwiZ2V0Q3Jld0RldGFpbHMiLCJhc3NpZ25lZFRvSUQiLCJjcmV3SW5mbyIsImZpbmQiLCJjcmV3IiwiaWQiLCJ0b1N0cmluZyIsImdldFZlc3NlbERldGFpbHMiLCJ2ZXNzZWxJRCIsInZlc3NlbHMiLCJ2ZXNzZWwiLCJTdGF0dXNCYWRnZSIsIm1haW50ZW5hbmNlQ2hlY2siLCJpc092ZXJkdWUiLCJpc092ZXJEdWUiLCJzdGF0dXMiLCJzdGF0dXNUZXh0IiwiaW5jbHVkZXMiLCJkYXlzIiwic3BhbiIsImNsYXNzTmFtZSIsIk1haW50ZW5hbmNlVGFibGUiLCJtYWludGVuYW5jZUNoZWNrcyIsInNob3dWZXNzZWwiLCJwYXRobmFtZSIsInNlYXJjaFBhcmFtcyIsInZlc3NlbEljb25EYXRhIiwiZ2V0VmVzc2VsV2l0aEljb24iLCJjb2x1bW5zIiwiYWNjZXNzb3JLZXkiLCJoZWFkZXIiLCJjb2x1bW4iLCJjZWxsIiwicm93Iiwib3JpZ2luYWwiLCJkaXYiLCJocmVmIiwibmFtZSIsImNyZWF0ZWQiLCJmb3JtYXQiLCJiYXNpY0NvbXBvbmVudCIsImFzc2lnbmVkVG8iLCJzb3J0aW5nRm4iLCJyb3dBIiwicm93QiIsInZhbHVlQSIsInZhbHVlQiIsImxvY2FsZUNvbXBhcmUiLCJjZWxsQWxpZ25tZW50IiwidmVzc2VsRGV0YWlscyIsInZlc3NlbElkIiwiZGlzcGxheVRleHQiLCJicmVha3BvaW50IiwiY3Jld0RldGFpbHMiLCJpbnZlbnRvcnkiLCJpdGVtIiwiZGF0YSIsInBhZ2VTaXplIiwic2hvd1Rvb2xiYXIiLCJUYXNrTGlzdCIsInNldE1haW50ZW5hbmNlQ2hlY2tzIiwiZmlsdGVyZWRNYWludGVuYW5jZUNoZWNrcyIsInNldEZpbHRlcmVkTWFpbnRlbmFuY2VDaGVja3MiLCJzZXRWZXNzZWxzIiwic2V0Q3Jld0luZm8iLCJzZXRGaWx0ZXIiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJrZXl3b3JkRmlsdGVyIiwic2V0S2V5d29yZEZpbHRlciIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJlZGl0X3Rhc2siLCJzZXRFZGl0X3Rhc2siLCJicCIsImZpbHRlcklzT3ZlckR1ZSIsInNldEZpbHRlcklzT3ZlckR1ZSIsImluaXRfcGVybWlzc2lvbnMiLCJxdWVyeU1haW50ZW5hbmNlQ2hlY2tzIiwiZmV0Y2hQb2xpY3kiLCJvbkNvbXBsZXRlZCIsInJlc3BvbnNlIiwicmVhZENvbXBvbmVudE1haW50ZW5hbmNlQ2hlY2tMaXN0Iiwibm9kZXMiLCJ0YXNrIiwiZGF5IiwiaGFuZGxlU2V0TWFpbnRlbmFuY2VDaGVja3MiLCJvbkVycm9yIiwiZXJyb3IiLCJjb25zb2xlIiwibG9hZE1haW50ZW5hbmNlQ2hlY2tzIiwidmFyaWFibGVzIiwiaW52ZW50b3J5SUQiLCJhcmNoaXZlZCIsImdldCIsImhhbmRsZVNldFZlc3NlbHMiLCJhY3RpdmVWZXNzZWxzIiwiYXBwZW5kZWREYXRhIiwicHVzaCIsImdldFZlc3NlbExpc3QiLCJvZmZsaW5lIiwidmVzc2VsTW9kZWwiLCJsb2FkVmVzc2VscyIsInF1ZXJ5VmVzc2VscyIsInF1ZXJ5VmVzc2VsUmVzcG9uc2UiLCJyZWFkVmVzc2VscyIsImdldEFsbCIsImxpbWl0Iiwib2Zmc2V0IiwidGFza3MiLCJBcnJheSIsImZyb20iLCJTZXQiLCJsb2FkQ3Jld01lbWJlckluZm8iLCJxdWVyeUNyZXdNZW1iZXJJbmZvIiwicmVhZFNlYUxvZ3NNZW1iZXJzIiwiY3Jld0lkIiwiY3Jld01lbWJlcklEcyIsImhhbmRsZUZpbHRlck9uQ2hhbmdlIiwidHlwZSIsInNlYXJjaEZpbHRlciIsImlzQXJyYXkiLCJiYXNpY0NvbXBvbmVudElEIiwiaW4iLCJ2YWx1ZSIsImVxIiwibWVtYmVySWQiLCJzdGFydERhdGUiLCJlbmREYXRlIiwiZXhwaXJlcyIsImd0ZSIsImx0ZSIsIm1haW50ZW5hbmNlQ2F0ZWdvcnlJRCIsInRhc2tUeXBlIiwia2V5RmlsdGVyIiwiY29udGFpbnMiLCJkb3dubG9hZENzdiIsImNzdkVudHJpZXMiLCJmb3JFYWNoIiwiYXNzaWduZWRUb05hbWUiLCJkb3dubG9hZFBkZiIsImhlYWRlcnMiLCJib2R5IiwiZ2V0U3RhdHVzQ29sb3JDbGFzc2VzIiwiY3JlYXRlTWFpbnRlbmFuY2VDb2x1bW5zIiwib3ZlckR1ZVN0YXR1cyIsIm92ZXJEdWVEYXlzIiwidGFza0xpbmsiLCJ2ZXNzZWxXaXRoSWNvbiIsImdldE1haW50ZW5hbmNlUm93U3RhdHVzIiwiaWNvbiIsImFjdGlvbnMiLCJ0aXRsZUNsYXNzTmFtZSIsIm9uQ2hhbmdlIiwicm93U3RhdHVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});