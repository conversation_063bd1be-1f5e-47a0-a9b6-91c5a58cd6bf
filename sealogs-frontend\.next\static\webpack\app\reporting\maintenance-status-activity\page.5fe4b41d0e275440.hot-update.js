"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/maintenance-status-activity/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../reporting/maintenance-status-activity-report */ \"(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\");\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 173,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 180,\n        columnNumber: 9\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_20__.useVesselIconData)();\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                var _maintenanceCheck_name;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_4___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 25\n                        }, this),\n                        !showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \" hover:text-curious-blue-400\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: [\n                                ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-outer-space-400\",\n                                            children: [\n                                                \"Assigned to:\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                            className: \"hover:text-curious-blue-400\",\n                                            children: maintenanceCheck.assignedTo.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                                        maintenanceCheck: maintenanceCheck\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        ...showVessel ? [] : [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 27\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_19__.VesselLocationDisplay, {\n                            vessel: vesselDetails,\n                            vesselId: maintenanceCheck.basicComponent.id,\n                            displayText: maintenanceCheck.basicComponent.title,\n                            showLink: true,\n                            className: \"gap-2.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 39\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 31\n                    }, this);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ],\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 37\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:text-curious-blue-400 hidden lg:block\",\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:text-curious-blue-400\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 405,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"Zp7KzlBBuyYf0TXkH/rj/yT9nuA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_20__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_20__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_21__.useBreakpoints)();\n    const [filterIsOverDue, setFilterIsOverDue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (permissions) {\n            init_permissions();\n        }\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_23__.ReadComponentMaintenanceCheckList, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].nodes;\n            if (data) {\n                if (filterIsOverDue) {\n                    const isOverDue = data.filter((task)=>{\n                        return task.isOverDue.day < 0;\n                    });\n                    handleSetMaintenanceChecks(isOverDue);\n                } else {\n                    handleSetMaintenanceChecks(data);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let filter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0,\n                archived: searchParams.get(\"archived\") === \"true\" ? 1 : 0,\n                filter: filter\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_23__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_23__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        // let filteredTasks = maintenanceChecks || []\n        setFilterIsOverDue(false);\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                if (data.value !== \"Overdue\") {\n                    searchFilter.status = {\n                        eq: data.value\n                    };\n                } else {\n                    delete searchFilter.status;\n                    setFilterIsOverDue(true);\n                }\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+(typeof item === \"object\" ? item.value : item))\n                };\n            } else if (data && !Array.isArray(data)) {\n                // Handle both object format {value: id} and direct ID value\n                const memberId = typeof data === \"object\" ? data.value : data;\n                searchFilter.assignedToID = {\n                    eq: +memberId\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(data.startDate).format(\"YYYY-MM-DD 00:00:00\"),\n                    lte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(data.endDate).format(\"YYYY-MM-DD 23:59:59\")\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        // let recurringFilter = null\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                searchFilter.taskType = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.taskType;\n            }\n        }\n        // // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\") {\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_6___default()(data === null || data === void 0 ? void 0 : data.value))) {\n                searchFilter.name = {\n                    contains: data === null || data === void 0 ? void 0 : data.value\n                };\n                keyFilter = data === null || data === void 0 ? void 0 : data.value;\n            } else {\n                delete searchFilter.name;\n                keyFilter = null;\n            }\n        } else if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(keyFilter)) {\n            searchFilter.name = {\n                contains: keyFilter !== null && keyFilter !== void 0 ? keyFilter : \"\"\n            };\n        } else {\n            delete searchFilter.name;\n            keyFilter = null;\n        }\n        // if (type === 'keyword' || (keyFilter && keyFilter.length > 0)) {\n        //     const keyword = data?.value?.trim().toLowerCase()\n        //     if (keyword && keyword.length > 0) {\n        //         filteredTasks = filteredTasks.filter(\n        //             (maintenanceCheck: MaintenanceCheck) =>\n        //                 [\n        //                     maintenanceCheck.name,\n        //                     maintenanceCheck.comments,\n        //                     maintenanceCheck.workOrderNumber,\n        //                 ].some((field) =>\n        //                     field?.toLowerCase().includes(keyword),\n        //                 ),\n        //         )\n        //         keyFilter = data.value\n        //     } else {\n        //         keyFilter = null\n        //     }\n        // }\n        // Filtering based on current searchFilter\n        // // Filter by vessel (basicComponentID)\n        // if (searchFilter.basicComponentID) {\n        //     const ids = searchFilter.basicComponentID.in || [\n        //         searchFilter.basicComponentID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.basicComponent?.id),\n        //     )\n        // }\n        // Filter by status\n        // if (searchFilter.status) {\n        //     const statuses = searchFilter.status.in || [searchFilter.status.eq]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         statuses.includes(mc.status),\n        //     )\n        // }\n        // // Filter by assignedToID\n        // if (searchFilter.assignedToID) {\n        //     const ids = searchFilter.assignedToID.in || [\n        //         searchFilter.assignedToID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.assignedTo?.id),\n        //     )\n        // }\n        // Filter by category\n        // if (searchFilter.maintenanceCategoryID) {\n        //     const ids = searchFilter.maintenanceCategoryID.in || [\n        //         searchFilter.maintenanceCategoryID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.maintenanceCategoryID),\n        //     )\n        // }\n        // // Filter by date range\n        // if (\n        //     searchFilter.expires &&\n        //     searchFilter.expires.gte &&\n        //     searchFilter.expires.lte\n        // ) {\n        //     filteredTasks = filteredTasks.filter(\n        //         (mc: MaintenanceCheck) =>\n        //             dayjs(mc.startDate).isAfter(\n        //                 dayjs(searchFilter.expires!.gte),\n        //             ) &&\n        //             dayjs(mc.startDate).isBefore(\n        //                 dayjs(searchFilter.expires!.lte),\n        //             ),\n        //     )\n        // }\n        // Filter by recurring status\n        // if (recurringFilter) {\n        //     if (recurringFilter === 'recurring') {\n        //         // Recurring tasks have recurringID > 0\n        //         filteredTasks = filteredTasks.filter(\n        //             (mc: MaintenanceCheck) => mc.recurringID > 0,\n        //         )\n        //     } else if (recurringFilter === 'one-off') {\n        //         // One-off tasks have recurringID = 0 or null\n        //         filteredTasks = filteredTasks.filter(\n        //             (mc: MaintenanceCheck) =>\n        //                 !mc.recurringID || mc.recurringID === 0,\n        //         )\n        //     }\n        // }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        // setFilteredMaintenanceChecks(filteredTasks)\n        loadMaintenanceChecks(searchFilter);\n    };\n    const downloadCsv = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task\",\n                \"location\",\n                \"assigned to\",\n                \"due\"\n            ]\n        ];\n        maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").forEach((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            csvEntries.push([\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const headers = [\n            [\n                \"Task Name\",\n                \"Location\",\n                \"Assigned To\",\n                \"Due\"\n            ]\n        ];\n        const body = maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").map((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            return [\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__.exportPdfTable)({\n            headers,\n            body\n        });\n    };\n    const getStatusColorClasses = (status)=>{\n        switch(status){\n            case \"High\":\n                return \"text-destructive hover:text-cinnabar-800\";\n            case \"Upcoming\":\n                return \"text-warning hover:text-fire-bush-500\";\n            default:\n                return \"hover:text-curious-blue-400\";\n        }\n    };\n    const createMaintenanceColumns = (crewInfo, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 872,\n                        columnNumber: 21\n                    }, this);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_basicComponent, _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_assignedTo1, _maintenanceCheck_inventory, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    var _maintenanceCheck_name;\n                    const taskLink = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_25__.cn)(\"leading-tight truncate\", getStatusColorClasses(overDueStatus)),\n                        children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_4___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 889,\n                        columnNumber: 25\n                    }, this);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"tablet-sm:hidden inline-flex overflow-auto items-center gap-1.5\",\n                                children: [\n                                    ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                            className: \"text-xs\",\n                                            children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid\",\n                                        children: [\n                                            taskLink,\n                                            ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:text-curious-blue-400\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 45\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 905,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden tablet-sm:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid items-center gap-2\",\n                                        children: taskLink\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 937,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: \"hover:text-curious-blue-400\",\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 970,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1, _maintenanceCheck_basicComponent2;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    // Get vessel with icon data\n                    const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) || 0, vesselDetails);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"min-w-fit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    vessel: vesselWithIcon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 49\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 993,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_17__.TooltipContent, {\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 991,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                    className: \"hover:text-curious-blue-400\",\n                                    children: maintenanceCheck.basicComponent.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 990,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"assigned\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Assigned\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1028,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-lg\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                    className: \"hover:text-curious-blue-400 hidden tablet-md:block\",\n                                    children: maintenanceCheck.assignedTo.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1042,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"inventory\",\n                header: \"Inventory\",\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-sm\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_inventory;\n                    const maintenanceCheck = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                            className: \"hover:text-curious-blue-400\",\n                            children: maintenanceCheck.inventory.item\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1080,\n                            columnNumber: 33\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1086,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1095,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"right\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const maintenanceCheck = row.original;\n                    if (!maintenanceCheck) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 32\n                        }, this);\n                    }\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" items-end w-fit text-sm xs:text-base py-0.5 px-1 xs:px-3 xs:py-1\\n                                                    \".concat(overDueStatus === \"High\" ? \"alert whitespace-nowrap\" : \"\", \"\\n                                                    \"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1112,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1119,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1124,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ]);\n    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon);\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_22__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1177,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1179,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1174,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1184,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_2__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1192,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1182,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"nONp+74DL7ZhxuCYhXSkcUYEX0s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_20__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_21__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});