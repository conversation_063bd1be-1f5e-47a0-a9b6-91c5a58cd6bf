"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/ui/maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions for generating initials (similar to maintenance list)\nconst getCrewInitials = (assignedTo)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!assignedTo) return \"??\";\n    const names = assignedTo.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Helper function to extract status text using the exact same logic as StatusBadge\n// This ensures consistency between visual display and exported data\nconst getStatusText = (isOverDue)=>{\n    let statusText = \"\";\n    if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(isOverDue.status)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) === \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Upcoming\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) !== \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    }\n    return statusText || \"\";\n};\n// Helper function to create a compatible MaintenanceCheck object for StatusBadge\nconst createMaintenanceCheckForBadge = (reportItem)=>{\n    return {\n        id: 0,\n        assignedTo: {\n            id: 0,\n            name: \"\"\n        },\n        basicComponent: {\n            id: 0,\n            title: null\n        },\n        inventory: {\n            id: 0,\n            item: null\n        },\n        status: reportItem.status || \"\",\n        recurringID: 0,\n        name: reportItem.taskName,\n        created: \"\",\n        severity: \"\",\n        isOverDue: reportItem.dueStatus,\n        comments: null,\n        workOrderNumber: null,\n        startDate: \"\",\n        expires: null,\n        maintenanceCategoryID: 0\n    };\n};\n// Helper function to get status color classes (similar to maintenance list)\nconst getStatusColorClasses = (status)=>{\n    switch(status){\n        case \"High\":\n            return \"text-destructive hover:text-cinnabar-800\";\n        case \"Upcoming\":\n            return \"text-warning hover:text-fire-bush-500\";\n        default:\n            return \"hover:text-curious-blue-400\";\n    }\n};\n// Function to create columns (will be called inside component to access bp and vessel data)\nconst createMaintenanceReportColumns = (bp, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Task Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus;\n                const item = row.original;\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const taskContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"leading-tight truncate font-medium\", getStatusColorClasses(overDueStatus)),\n                    children: item.taskName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 21\n                }, undefined);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"tablet-sm:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        taskContent,\n                                        item.inventoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-curious-blue-400 text-sm\",\n                                                children: item.inventoryName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden tablet-sm:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: taskContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: item.vesselName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-outer-space-400\",\n                                                    children: [\n                                                        \"Location:\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hover:text-curious-blue-400\",\n                                                    children: item.vesselName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.taskName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.taskName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventoryName\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.inventoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hover:text-curious-blue-400\",\n                        children: item.inventoryName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.inventoryName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.inventoryName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Location\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Create a vessel object for VesselIcon (we don't have vessel ID in report data)\n                const vesselForIcon = {\n                    id: 0,\n                    title: item.vesselName\n                };\n                const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                    vessel: vesselWithIcon,\n                    displayText: item.vesselName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"assignedTo\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(item.assignedTo)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hover:text-curious-blue-400 hidden tablet-md:block\",\n                                children: item.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.assignedTo) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.assignedTo) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.status || \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.status) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.status) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"dueDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"dueStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus, _item_dueStatus1;\n                const item = row.original;\n                const maintenanceCheck = createMaintenanceCheckForBadge(item);\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 28\n                    }, undefined);\n                }\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const overDueDays = (_item_dueStatus1 = item.dueStatus) === null || _item_dueStatus1 === void 0 ? void 0 : _item_dueStatus1.days;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n                        children: overDueDays || \"Overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 33\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 33\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_dueStatus, _rowA_original, _rowB_original_dueStatus, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_dueStatus = _rowA_original.dueStatus) === null || _rowA_original_dueStatus === void 0 ? void 0 : _rowA_original_dueStatus.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_dueStatus = _rowB_original.dueStatus) === null || _rowB_original_dueStatus === void 0 ? void 0 : _rowB_original_dueStatus.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n// Row status evaluator for maintenance report (similar to maintenance list)\nconst getMaintenanceReportRowStatus = (reportItem)=>{\n    var _reportItem_dueStatus;\n    // Skip completed, archived, or draft tasks\n    if (reportItem.status === \"Completed\" || reportItem.status === \"Save_As_Draft\") {\n        return \"normal\";\n    }\n    const overDueStatus = (_reportItem_dueStatus = reportItem.dueStatus) === null || _reportItem_dueStatus === void 0 ? void 0 : _reportItem_dueStatus.status;\n    // Use the pre-calculated status values from the system\n    switch(overDueStatus){\n        case \"High\":\n            return \"overdue\" // Red highlighting\n            ;\n        case \"Upcoming\":\n            return \"upcoming\" // Orange highlighting\n            ;\n        case \"Medium\":\n        case \"Open\":\n        default:\n            return \"normal\" // No highlighting\n            ;\n    }\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    // Create columns with access to bp and vessel icon data\n    const columns = createMaintenanceReportColumns(bp, getVesselWithIcon);\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readComponentMaintenanceChecks_nodes, _data_readComponentMaintenanceChecks;\n            // Log unique status values to understand what's available\n            const statusValues = new Set();\n            data === null || data === void 0 ? void 0 : (_data_readComponentMaintenanceChecks = data.readComponentMaintenanceChecks) === null || _data_readComponentMaintenanceChecks === void 0 ? void 0 : (_data_readComponentMaintenanceChecks_nodes = _data_readComponentMaintenanceChecks.nodes) === null || _data_readComponentMaintenanceChecks_nodes === void 0 ? void 0 : _data_readComponentMaintenanceChecks_nodes.forEach((node)=>{\n                if (node.status) {\n                    statusValues.add(node.status);\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"❌ GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error:\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>+item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: +category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: +crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                getStatusText(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                getStatusText(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__.MaintenanceReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 621,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 618,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    rowStatus: getMaintenanceReportRowStatus,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 628,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 627,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"awGifJwGcWqIoMGkhaK/iBXUwB4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});