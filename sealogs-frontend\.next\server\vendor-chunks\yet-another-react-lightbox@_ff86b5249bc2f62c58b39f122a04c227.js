"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227";
exports.ids = ["vendor-chunks/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/captions/captions.css":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/captions/captions.css ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0bccefafe1ce\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veWV0LWFub3RoZXItcmVhY3QtbGlnaHRib3hAX2ZmODZiNTI0OWJjMmY2MmM1OGIzOWYxMjJhMDRjMjI3L25vZGVfbW9kdWxlcy95ZXQtYW5vdGhlci1yZWFjdC1saWdodGJveC9kaXN0L3BsdWdpbnMvY2FwdGlvbnMvY2FwdGlvbnMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS95ZXQtYW5vdGhlci1yZWFjdC1saWdodGJveEBfZmY4NmI1MjQ5YmMyZjYyYzU4YjM5ZjEyMmEwNGMyMjcvbm9kZV9tb2R1bGVzL3lldC1hbm90aGVyLXJlYWN0LWxpZ2h0Ym94L2Rpc3QvcGx1Z2lucy9jYXB0aW9ucy9jYXB0aW9ucy5jc3M/NTJiNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBiY2NlZmFmZTFjZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/captions/captions.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/styles.css":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/styles.css ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"075a87508db5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veWV0LWFub3RoZXItcmVhY3QtbGlnaHRib3hAX2ZmODZiNTI0OWJjMmY2MmM1OGIzOWYxMjJhMDRjMjI3L25vZGVfbW9kdWxlcy95ZXQtYW5vdGhlci1yZWFjdC1saWdodGJveC9kaXN0L3N0eWxlcy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL3lldC1hbm90aGVyLXJlYWN0LWxpZ2h0Ym94QF9mZjg2YjUyNDliYzJmNjJjNThiMzlmMTIyYTA0YzIyNy9ub2RlX21vZHVsZXMveWV0LWFub3RoZXItcmVhY3QtbGlnaHRib3gvZGlzdC9zdHlsZXMuY3NzPzgzMmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwNzVhODc1MDhkYjVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/styles.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/index.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/index.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_CLOSE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE),\n/* harmony export */   ACTION_NEXT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT),\n/* harmony export */   ACTION_PREV: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV),\n/* harmony export */   ACTION_SWIPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE),\n/* harmony export */   ACTIVE_SLIDE_COMPLETE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_COMPLETE),\n/* harmony export */   ACTIVE_SLIDE_ERROR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_ERROR),\n/* harmony export */   ACTIVE_SLIDE_LOADING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_LOADING),\n/* harmony export */   ACTIVE_SLIDE_PLAYING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_PLAYING),\n/* harmony export */   CLASS_FLEX_CENTER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER),\n/* harmony export */   CLASS_FULLSIZE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FULLSIZE),\n/* harmony export */   CLASS_NO_SCROLL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL),\n/* harmony export */   CLASS_NO_SCROLL_PADDING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING),\n/* harmony export */   CLASS_SLIDE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE),\n/* harmony export */   CLASS_SLIDE_WRAPPER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER),\n/* harmony export */   CLASS_SLIDE_WRAPPER_INTERACTIVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER_INTERACTIVE),\n/* harmony export */   Carousel: () => (/* binding */ Carousel),\n/* harmony export */   CarouselModule: () => (/* binding */ CarouselModule),\n/* harmony export */   CloseIcon: () => (/* binding */ CloseIcon),\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   ControllerContext: () => (/* binding */ ControllerContext),\n/* harmony export */   ControllerModule: () => (/* binding */ ControllerModule),\n/* harmony export */   DocumentContext: () => (/* binding */ DocumentContext),\n/* harmony export */   DocumentContextProvider: () => (/* binding */ DocumentContextProvider),\n/* harmony export */   ELEMENT_BUTTON: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_BUTTON),\n/* harmony export */   ELEMENT_ICON: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON),\n/* harmony export */   EVENT_ON_KEY_DOWN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN),\n/* harmony export */   EVENT_ON_KEY_UP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_UP),\n/* harmony export */   EVENT_ON_POINTER_CANCEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL),\n/* harmony export */   EVENT_ON_POINTER_DOWN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN),\n/* harmony export */   EVENT_ON_POINTER_LEAVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE),\n/* harmony export */   EVENT_ON_POINTER_MOVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE),\n/* harmony export */   EVENT_ON_POINTER_UP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP),\n/* harmony export */   EVENT_ON_WHEEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL),\n/* harmony export */   ErrorIcon: () => (/* binding */ ErrorIcon),\n/* harmony export */   EventsContext: () => (/* binding */ EventsContext),\n/* harmony export */   EventsProvider: () => (/* binding */ EventsProvider),\n/* harmony export */   IMAGE_FIT_CONTAIN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN),\n/* harmony export */   IMAGE_FIT_COVER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER),\n/* harmony export */   IconButton: () => (/* binding */ IconButton),\n/* harmony export */   ImageSlide: () => (/* binding */ ImageSlide),\n/* harmony export */   Lightbox: () => (/* binding */ Lightbox),\n/* harmony export */   LightboxDefaultProps: () => (/* binding */ LightboxDefaultProps),\n/* harmony export */   LightboxDispatchContext: () => (/* binding */ LightboxDispatchContext),\n/* harmony export */   LightboxPropsContext: () => (/* binding */ LightboxPropsContext),\n/* harmony export */   LightboxPropsProvider: () => (/* binding */ LightboxPropsProvider),\n/* harmony export */   LightboxRoot: () => (/* binding */ LightboxRoot),\n/* harmony export */   LightboxStateContext: () => (/* binding */ LightboxStateContext),\n/* harmony export */   LightboxStateProvider: () => (/* binding */ LightboxStateProvider),\n/* harmony export */   LoadingIcon: () => (/* binding */ LoadingIcon),\n/* harmony export */   MODULE_CAROUSEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL),\n/* harmony export */   MODULE_CONTROLLER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER),\n/* harmony export */   MODULE_NAVIGATION: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NAVIGATION),\n/* harmony export */   MODULE_NO_SCROLL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NO_SCROLL),\n/* harmony export */   MODULE_PORTAL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL),\n/* harmony export */   MODULE_ROOT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_ROOT),\n/* harmony export */   MODULE_TOOLBAR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR),\n/* harmony export */   Navigation: () => (/* binding */ Navigation),\n/* harmony export */   NavigationButton: () => (/* binding */ NavigationButton),\n/* harmony export */   NavigationModule: () => (/* binding */ NavigationModule),\n/* harmony export */   NextIcon: () => (/* binding */ NextIcon),\n/* harmony export */   NoScroll: () => (/* binding */ NoScroll),\n/* harmony export */   NoScrollModule: () => (/* binding */ NoScrollModule),\n/* harmony export */   PLUGIN_CAPTIONS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_CAPTIONS),\n/* harmony export */   PLUGIN_COUNTER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_COUNTER),\n/* harmony export */   PLUGIN_DOWNLOAD: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_DOWNLOAD),\n/* harmony export */   PLUGIN_FULLSCREEN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN),\n/* harmony export */   PLUGIN_INLINE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_INLINE),\n/* harmony export */   PLUGIN_SHARE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_SHARE),\n/* harmony export */   PLUGIN_SLIDESHOW: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_SLIDESHOW),\n/* harmony export */   PLUGIN_THUMBNAILS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_THUMBNAILS),\n/* harmony export */   PLUGIN_ZOOM: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   PortalModule: () => (/* binding */ PortalModule),\n/* harmony export */   PreviousIcon: () => (/* binding */ PreviousIcon),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RootModule: () => (/* binding */ RootModule),\n/* harmony export */   SLIDE_STATUS_COMPLETE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE),\n/* harmony export */   SLIDE_STATUS_ERROR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR),\n/* harmony export */   SLIDE_STATUS_LOADING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING),\n/* harmony export */   SLIDE_STATUS_PLACEHOLDER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLACEHOLDER),\n/* harmony export */   SLIDE_STATUS_PLAYING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLAYING),\n/* harmony export */   SwipeState: () => (/* binding */ SwipeState),\n/* harmony export */   TimeoutsContext: () => (/* binding */ TimeoutsContext),\n/* harmony export */   TimeoutsProvider: () => (/* binding */ TimeoutsProvider),\n/* harmony export */   Toolbar: () => (/* binding */ Toolbar),\n/* harmony export */   ToolbarModule: () => (/* binding */ ToolbarModule),\n/* harmony export */   UNKNOWN_ACTION_TYPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE),\n/* harmony export */   VK_ARROW_LEFT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_LEFT),\n/* harmony export */   VK_ARROW_RIGHT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_RIGHT),\n/* harmony export */   VK_ESCAPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ESCAPE),\n/* harmony export */   activeSlideStatus: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.activeSlideStatus),\n/* harmony export */   addToolbarButton: () => (/* binding */ addToolbarButton),\n/* harmony export */   calculatePreload: () => (/* binding */ calculatePreload),\n/* harmony export */   cleanup: () => (/* binding */ cleanup),\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   composePrefix: () => (/* binding */ composePrefix),\n/* harmony export */   computeSlideRect: () => (/* binding */ computeSlideRect),\n/* harmony export */   createIcon: () => (/* binding */ createIcon),\n/* harmony export */   createIconDisabled: () => (/* binding */ createIconDisabled),\n/* harmony export */   createModule: () => (/* binding */ createModule),\n/* harmony export */   createNode: () => (/* binding */ createNode),\n/* harmony export */   cssClass: () => (/* binding */ cssClass),\n/* harmony export */   cssVar: () => (/* binding */ cssVar),\n/* harmony export */   \"default\": () => (/* binding */ Lightbox),\n/* harmony export */   devicePixelRatio: () => (/* binding */ devicePixelRatio),\n/* harmony export */   getSlide: () => (/* binding */ getSlide),\n/* harmony export */   getSlideIfPresent: () => (/* binding */ getSlideIfPresent),\n/* harmony export */   getSlideIndex: () => (/* binding */ getSlideIndex),\n/* harmony export */   getSlideKey: () => (/* binding */ getSlideKey),\n/* harmony export */   hasSlides: () => (/* binding */ hasSlides),\n/* harmony export */   hasWindow: () => (/* binding */ hasWindow),\n/* harmony export */   isImageFitCover: () => (/* binding */ isImageFitCover),\n/* harmony export */   isImageSlide: () => (/* binding */ isImageSlide),\n/* harmony export */   label: () => (/* binding */ label),\n/* harmony export */   makeComposePrefix: () => (/* binding */ makeComposePrefix),\n/* harmony export */   makeInertWhen: () => (/* binding */ makeInertWhen),\n/* harmony export */   makeUseContext: () => (/* binding */ makeUseContext),\n/* harmony export */   parseInt: () => (/* binding */ parseInt),\n/* harmony export */   parseLengthPercentage: () => (/* binding */ parseLengthPercentage),\n/* harmony export */   reflow: () => (/* binding */ reflow),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   setRef: () => (/* binding */ setRef),\n/* harmony export */   stopNavigationEventsPropagation: () => (/* binding */ stopNavigationEventsPropagation),\n/* harmony export */   useAnimation: () => (/* binding */ useAnimation),\n/* harmony export */   useContainerRect: () => (/* binding */ useContainerRect),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useDelay: () => (/* binding */ useDelay),\n/* harmony export */   useDocumentContext: () => (/* binding */ useDocumentContext),\n/* harmony export */   useEventCallback: () => (/* binding */ useEventCallback),\n/* harmony export */   useEvents: () => (/* binding */ useEvents),\n/* harmony export */   useForkRef: () => (/* binding */ useForkRef),\n/* harmony export */   useKeyboardNavigation: () => (/* binding */ useKeyboardNavigation),\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect),\n/* harmony export */   useLightboxDispatch: () => (/* binding */ useLightboxDispatch),\n/* harmony export */   useLightboxProps: () => (/* binding */ useLightboxProps),\n/* harmony export */   useLightboxState: () => (/* binding */ useLightboxState),\n/* harmony export */   useLoseFocus: () => (/* binding */ useLoseFocus),\n/* harmony export */   useMotionPreference: () => (/* binding */ useMotionPreference),\n/* harmony export */   useNavigationState: () => (/* binding */ useNavigationState),\n/* harmony export */   usePointerEvents: () => (/* binding */ usePointerEvents),\n/* harmony export */   usePointerSwipe: () => (/* binding */ usePointerSwipe),\n/* harmony export */   usePreventWheelDefaults: () => (/* binding */ usePreventWheelDefaults),\n/* harmony export */   useRTL: () => (/* binding */ useRTL),\n/* harmony export */   useSensors: () => (/* binding */ useSensors),\n/* harmony export */   useThrottle: () => (/* binding */ useThrottle),\n/* harmony export */   useTimeouts: () => (/* binding */ useTimeouts),\n/* harmony export */   useWheelSwipe: () => (/* binding */ useWheelSwipe),\n/* harmony export */   withPlugins: () => (/* binding */ withPlugins)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/types.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ ACTIVE_SLIDE_COMPLETE,ACTIVE_SLIDE_ERROR,ACTIVE_SLIDE_LOADING,ACTIVE_SLIDE_PLAYING,CLASS_FULLSIZE,CLASS_SLIDE_WRAPPER_INTERACTIVE,PLUGIN_CAPTIONS,PLUGIN_COUNTER,PLUGIN_DOWNLOAD,PLUGIN_FULLSCREEN,PLUGIN_INLINE,PLUGIN_SHARE,PLUGIN_SLIDESHOW,PLUGIN_THUMBNAILS,PLUGIN_ZOOM,SLIDE_STATUS_PLAYING,ACTION_CLOSE,ACTION_NEXT,ACTION_PREV,ACTION_SWIPE,CLASS_FLEX_CENTER,CLASS_NO_SCROLL,CLASS_NO_SCROLL_PADDING,CLASS_SLIDE,CLASS_SLIDE_WRAPPER,Carousel,CarouselModule,CloseIcon,Controller,ControllerContext,ControllerModule,DocumentContext,DocumentContextProvider,ELEMENT_BUTTON,ELEMENT_ICON,EVENT_ON_KEY_DOWN,EVENT_ON_KEY_UP,EVENT_ON_POINTER_CANCEL,EVENT_ON_POINTER_DOWN,EVENT_ON_POINTER_LEAVE,EVENT_ON_POINTER_MOVE,EVENT_ON_POINTER_UP,EVENT_ON_WHEEL,ErrorIcon,EventsContext,EventsProvider,IMAGE_FIT_CONTAIN,IMAGE_FIT_COVER,IconButton,ImageSlide,Lightbox,LightboxDefaultProps,LightboxDispatchContext,LightboxPropsContext,LightboxPropsProvider,LightboxRoot,LightboxStateContext,LightboxStateProvider,LoadingIcon,MODULE_CAROUSEL,MODULE_CONTROLLER,MODULE_NAVIGATION,MODULE_NO_SCROLL,MODULE_PORTAL,MODULE_ROOT,MODULE_TOOLBAR,Navigation,NavigationButton,NavigationModule,NextIcon,NoScroll,NoScrollModule,Portal,PortalModule,PreviousIcon,Root,RootModule,SLIDE_STATUS_COMPLETE,SLIDE_STATUS_ERROR,SLIDE_STATUS_LOADING,SLIDE_STATUS_PLACEHOLDER,SwipeState,TimeoutsContext,TimeoutsProvider,Toolbar,ToolbarModule,UNKNOWN_ACTION_TYPE,VK_ARROW_LEFT,VK_ARROW_RIGHT,VK_ESCAPE,activeSlideStatus,addToolbarButton,calculatePreload,cleanup,clsx,composePrefix,computeSlideRect,createIcon,createIconDisabled,createModule,createNode,cssClass,cssVar,default,devicePixelRatio,getSlide,getSlideIfPresent,getSlideIndex,getSlideKey,hasSlides,hasWindow,isImageFitCover,isImageSlide,label,makeComposePrefix,makeInertWhen,makeUseContext,parseInt,parseLengthPercentage,reflow,round,setRef,stopNavigationEventsPropagation,useAnimation,useContainerRect,useController,useDelay,useDocumentContext,useEventCallback,useEvents,useForkRef,useKeyboardNavigation,useLayoutEffect,useLightboxDispatch,useLightboxProps,useLightboxState,useLoseFocus,useMotionPreference,useNavigationState,usePointerEvents,usePointerSwipe,usePreventWheelDefaults,useRTL,useSensors,useThrottle,useTimeouts,useWheelSwipe,withPlugins auto */ \n\n\n\nconst cssPrefix$3 = \"yarl__\";\nfunction clsx(...classes) {\n    return [\n        ...classes\n    ].filter(Boolean).join(\" \");\n}\nfunction cssClass(name) {\n    return `${cssPrefix$3}${name}`;\n}\nfunction cssVar(name) {\n    return `--${cssPrefix$3}${name}`;\n}\nfunction composePrefix(base, prefix) {\n    return `${base}${prefix ? `_${prefix}` : \"\"}`;\n}\nfunction makeComposePrefix(base) {\n    return (prefix)=>composePrefix(base, prefix);\n}\nfunction label(labels, defaultLabel) {\n    var _a;\n    return (_a = labels === null || labels === void 0 ? void 0 : labels[defaultLabel]) !== null && _a !== void 0 ? _a : defaultLabel;\n}\nfunction cleanup(...cleaners) {\n    return ()=>{\n        cleaners.forEach((cleaner)=>{\n            cleaner();\n        });\n    };\n}\nfunction makeUseContext(name, contextName, context) {\n    return ()=>{\n        const ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n        if (!ctx) {\n            throw new Error(`${name} must be used within a ${contextName}.Provider`);\n        }\n        return ctx;\n    };\n}\nfunction hasWindow() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction round(value, decimals = 0) {\n    const factor = 10 ** decimals;\n    return Math.round((value + Number.EPSILON) * factor) / factor;\n}\nfunction isImageSlide(slide) {\n    return slide.type === undefined || slide.type === \"image\";\n}\nfunction isImageFitCover(image, imageFit) {\n    return image.imageFit === _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER || image.imageFit !== _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN && imageFit === _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER;\n}\nfunction parseInt(value) {\n    return typeof value === \"string\" ? Number.parseInt(value, 10) : value;\n}\nfunction parseLengthPercentage(input) {\n    if (typeof input === \"number\") {\n        return {\n            pixel: input\n        };\n    }\n    if (typeof input === \"string\") {\n        const value = parseInt(input);\n        return input.endsWith(\"%\") ? {\n            percent: value\n        } : {\n            pixel: value\n        };\n    }\n    return {\n        pixel: 0\n    };\n}\nfunction computeSlideRect(containerRect, padding) {\n    const paddingValue = parseLengthPercentage(padding);\n    const paddingPixels = paddingValue.percent !== undefined ? containerRect.width / 100 * paddingValue.percent : paddingValue.pixel;\n    return {\n        width: Math.max(containerRect.width - 2 * paddingPixels, 0),\n        height: Math.max(containerRect.height - 2 * paddingPixels, 0)\n    };\n}\nfunction devicePixelRatio() {\n    return (hasWindow() ? window === null || window === void 0 ? void 0 : window.devicePixelRatio : undefined) || 1;\n}\nfunction getSlideIndex(index, slidesCount) {\n    return slidesCount > 0 ? (index % slidesCount + slidesCount) % slidesCount : 0;\n}\nfunction hasSlides(slides) {\n    return slides.length > 0;\n}\nfunction getSlide(slides, index) {\n    return slides[getSlideIndex(index, slides.length)];\n}\nfunction getSlideIfPresent(slides, index) {\n    return hasSlides(slides) ? getSlide(slides, index) : undefined;\n}\nfunction getSlideKey(slide) {\n    return isImageSlide(slide) ? slide.src : undefined;\n}\nfunction addToolbarButton(toolbar, key, button) {\n    if (!button) return toolbar;\n    const { buttons, ...restToolbar } = toolbar;\n    const index = buttons.findIndex((item)=>item === key);\n    const buttonWithKey = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(button) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(button, {\n        key\n    }, null) : button;\n    if (index >= 0) {\n        const result = [\n            ...buttons\n        ];\n        result.splice(index, 1, buttonWithKey);\n        return {\n            buttons: result,\n            ...restToolbar\n        };\n    }\n    return {\n        buttons: [\n            buttonWithKey,\n            ...buttons\n        ],\n        ...restToolbar\n    };\n}\nfunction stopNavigationEventsPropagation() {\n    const stopPropagation = (event)=>{\n        event.stopPropagation();\n    };\n    return {\n        onPointerDown: stopPropagation,\n        onKeyDown: stopPropagation,\n        onWheel: stopPropagation\n    };\n}\nfunction calculatePreload(carousel, slides, minimum = 0) {\n    return Math.min(carousel.preload, Math.max(carousel.finite ? slides.length - 1 : Math.floor(slides.length / 2), minimum));\n}\nconst isReact19 = Number(react__WEBPACK_IMPORTED_MODULE_0__.version.split(\".\")[0]) >= 19;\nfunction makeInertWhen(condition) {\n    const legacyValue = condition ? \"\" : undefined;\n    return {\n        inert: isReact19 ? condition : legacyValue\n    };\n}\nfunction reflow(node) {\n    node.scrollTop;\n}\nconst LightboxDefaultProps = {\n    open: false,\n    close: ()=>{},\n    index: 0,\n    slides: [],\n    render: {},\n    plugins: [],\n    toolbar: {\n        buttons: [\n            _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE\n        ]\n    },\n    labels: {},\n    animation: {\n        fade: 250,\n        swipe: 500,\n        easing: {\n            fade: \"ease\",\n            swipe: \"ease-out\",\n            navigation: \"ease-in-out\"\n        }\n    },\n    carousel: {\n        finite: false,\n        preload: 2,\n        padding: \"16px\",\n        spacing: \"30%\",\n        imageFit: _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN,\n        imageProps: {}\n    },\n    controller: {\n        ref: null,\n        focus: true,\n        aria: false,\n        touchAction: \"none\",\n        closeOnPullUp: false,\n        closeOnPullDown: false,\n        closeOnBackdropClick: false,\n        preventDefaultWheelX: true,\n        preventDefaultWheelY: false,\n        disableSwipeNavigation: false\n    },\n    portal: {},\n    noScroll: {\n        disabled: false\n    },\n    on: {},\n    styles: {},\n    className: \"\"\n};\nfunction createModule(name, component) {\n    return {\n        name,\n        component\n    };\n}\nfunction createNode(module, children) {\n    return {\n        module,\n        children\n    };\n}\nfunction traverseNode(node, target, apply) {\n    if (node.module.name === target) {\n        return apply(node);\n    }\n    if (node.children) {\n        return [\n            createNode(node.module, node.children.flatMap((n)=>{\n                var _a;\n                return (_a = traverseNode(n, target, apply)) !== null && _a !== void 0 ? _a : [];\n            }))\n        ];\n    }\n    return [\n        node\n    ];\n}\nfunction traverse(nodes, target, apply) {\n    return nodes.flatMap((node)=>{\n        var _a;\n        return (_a = traverseNode(node, target, apply)) !== null && _a !== void 0 ? _a : [];\n    });\n}\nfunction withPlugins(root, plugins = [], augmentations = []) {\n    let config = root;\n    const contains = (target)=>{\n        const nodes = [\n            ...config\n        ];\n        while(nodes.length > 0){\n            const node = nodes.pop();\n            if ((node === null || node === void 0 ? void 0 : node.module.name) === target) return true;\n            if (node === null || node === void 0 ? void 0 : node.children) nodes.push(...node.children);\n        }\n        return false;\n    };\n    const addParent = (target, module)=>{\n        if (target === \"\") {\n            config = [\n                createNode(module, config)\n            ];\n            return;\n        }\n        config = traverse(config, target, (node)=>[\n                createNode(module, [\n                    node\n                ])\n            ]);\n    };\n    const append = (target, module)=>{\n        config = traverse(config, target, (node)=>[\n                createNode(node.module, [\n                    createNode(module, node.children)\n                ])\n            ]);\n    };\n    const addChild = (target, module, precede)=>{\n        config = traverse(config, target, (node)=>{\n            var _a;\n            return [\n                createNode(node.module, [\n                    ...precede ? [\n                        createNode(module)\n                    ] : [],\n                    ...(_a = node.children) !== null && _a !== void 0 ? _a : [],\n                    ...!precede ? [\n                        createNode(module)\n                    ] : []\n                ])\n            ];\n        });\n    };\n    const addSibling = (target, module, precede)=>{\n        config = traverse(config, target, (node)=>[\n                ...precede ? [\n                    createNode(module)\n                ] : [],\n                node,\n                ...!precede ? [\n                    createNode(module)\n                ] : []\n            ]);\n    };\n    const addModule = (module)=>{\n        append(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER, module);\n    };\n    const replace = (target, module)=>{\n        config = traverse(config, target, (node)=>[\n                createNode(module, node.children)\n            ]);\n    };\n    const remove = (target)=>{\n        config = traverse(config, target, (node)=>node.children);\n    };\n    const augment = (augmentation)=>{\n        augmentations.push(augmentation);\n    };\n    plugins.forEach((plugin)=>{\n        plugin({\n            contains,\n            addParent,\n            append,\n            addChild,\n            addSibling,\n            addModule,\n            replace,\n            remove,\n            augment\n        });\n    });\n    return {\n        config,\n        augmentation: (props)=>augmentations.reduce((acc, augmentation)=>augmentation(acc), props)\n    };\n}\nconst DocumentContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useDocumentContext = makeUseContext(\"useDocument\", \"DocumentContext\", DocumentContext);\nfunction DocumentContextProvider({ nodeRef, children }) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const getOwnerDocument = (node)=>{\n            var _a;\n            return ((_a = node || nodeRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || document;\n        };\n        const getOwnerWindow = (node)=>{\n            var _a;\n            return ((_a = getOwnerDocument(node)) === null || _a === void 0 ? void 0 : _a.defaultView) || window;\n        };\n        return {\n            getOwnerDocument,\n            getOwnerWindow\n        };\n    }, [\n        nodeRef\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DocumentContext.Provider, {\n        value: context\n    }, children);\n}\nconst EventsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useEvents = makeUseContext(\"useEvents\", \"EventsContext\", EventsContext);\nfunction EventsProvider({ children }) {\n    const [subscriptions] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>{\n            Object.keys(subscriptions).forEach((topic)=>delete subscriptions[topic]);\n        }, [\n        subscriptions\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const unsubscribe = (topic, callback)=>{\n            var _a;\n            (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.splice(0, subscriptions[topic].length, ...subscriptions[topic].filter((cb)=>cb !== callback));\n        };\n        const subscribe = (topic, callback)=>{\n            if (!subscriptions[topic]) {\n                subscriptions[topic] = [];\n            }\n            subscriptions[topic].push(callback);\n            return ()=>unsubscribe(topic, callback);\n        };\n        const publish = (...[topic, event])=>{\n            var _a;\n            (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.forEach((callback)=>callback(event));\n        };\n        return {\n            publish,\n            subscribe,\n            unsubscribe\n        };\n    }, [\n        subscriptions\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(EventsContext.Provider, {\n        value: context\n    }, children);\n}\nconst LightboxPropsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxProps = makeUseContext(\"useLightboxProps\", \"LightboxPropsContext\", LightboxPropsContext);\nfunction LightboxPropsProvider({ children, ...props }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxPropsContext.Provider, {\n        value: props\n    }, children);\n}\nconst LightboxStateContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxState = makeUseContext(\"useLightboxState\", \"LightboxStateContext\", LightboxStateContext);\nconst LightboxDispatchContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxDispatch = makeUseContext(\"useLightboxDispatch\", \"LightboxDispatchContext\", LightboxDispatchContext);\nfunction reducer(state, action) {\n    switch(action.type){\n        case \"swipe\":\n            {\n                const { slides } = state;\n                const increment = (action === null || action === void 0 ? void 0 : action.increment) || 0;\n                const globalIndex = state.globalIndex + increment;\n                const currentIndex = getSlideIndex(globalIndex, slides.length);\n                const currentSlide = getSlideIfPresent(slides, currentIndex);\n                const animation = increment || action.duration !== undefined ? {\n                    increment,\n                    duration: action.duration,\n                    easing: action.easing\n                } : undefined;\n                return {\n                    slides,\n                    currentIndex,\n                    globalIndex,\n                    currentSlide,\n                    animation\n                };\n            }\n        case \"update\":\n            if (action.slides !== state.slides || action.index !== state.currentIndex) {\n                return {\n                    slides: action.slides,\n                    currentIndex: action.index,\n                    globalIndex: action.index,\n                    currentSlide: getSlideIfPresent(action.slides, action.index)\n                };\n            }\n            return state;\n        default:\n            throw new Error(_types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE);\n    }\n}\nfunction LightboxStateProvider({ slides, index, children }) {\n    const [state, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(reducer, {\n        slides,\n        currentIndex: index,\n        globalIndex: index,\n        currentSlide: getSlideIfPresent(slides, index)\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        dispatch({\n            type: \"update\",\n            slides,\n            index\n        });\n    }, [\n        slides,\n        index\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            ...state,\n            state,\n            dispatch\n        }), [\n        state,\n        dispatch\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxDispatchContext.Provider, {\n        value: dispatch\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxStateContext.Provider, {\n        value: context\n    }, children));\n}\nconst TimeoutsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useTimeouts = makeUseContext(\"useTimeouts\", \"TimeoutsContext\", TimeoutsContext);\nfunction TimeoutsProvider({ children }) {\n    const [timeouts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>{\n            timeouts.forEach((tid)=>window.clearTimeout(tid));\n            timeouts.splice(0, timeouts.length);\n        }, [\n        timeouts\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const removeTimeout = (id)=>{\n            timeouts.splice(0, timeouts.length, ...timeouts.filter((tid)=>tid !== id));\n        };\n        const setTimeout = (fn, delay)=>{\n            const id = window.setTimeout(()=>{\n                removeTimeout(id);\n                fn();\n            }, delay);\n            timeouts.push(id);\n            return id;\n        };\n        const clearTimeout = (id)=>{\n            if (id !== undefined) {\n                removeTimeout(id);\n                window.clearTimeout(id);\n            }\n        };\n        return {\n            setTimeout,\n            clearTimeout\n        };\n    }, [\n        timeouts\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TimeoutsContext.Provider, {\n        value: context\n    }, children);\n}\nconst IconButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function IconButton({ label: label$1, className, icon: Icon, renderIcon, onClick, style, ...rest }, ref) {\n    const { styles, labels } = useLightboxProps();\n    const buttonLabel = label(labels, label$1);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        ref: ref,\n        type: \"button\",\n        title: buttonLabel,\n        \"aria-label\": buttonLabel,\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_BUTTON), className),\n        onClick: onClick,\n        style: {\n            ...style,\n            ...styles.button\n        },\n        ...rest\n    }, renderIcon ? renderIcon() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Icon, {\n        className: cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON),\n        style: styles.icon\n    }));\n});\nfunction svgIcon(name, children) {\n    const icon = (props)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            width: \"24\",\n            height: \"24\",\n            \"aria-hidden\": \"true\",\n            focusable: \"false\",\n            ...props\n        }, children);\n    icon.displayName = name;\n    return icon;\n}\nfunction createIcon(name, glyph) {\n    return svgIcon(name, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"g\", {\n        fill: \"currentColor\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"none\"\n    }), glyph));\n}\nfunction createIconDisabled(name, glyph) {\n    return svgIcon(name, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"defs\", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"mask\", {\n        id: \"strike\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"white\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0L24 24\",\n        stroke: \"black\",\n        strokeWidth: 4\n    }))), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0.70707 2.121320L21.878680 23.292883\",\n        stroke: \"currentColor\",\n        strokeWidth: 2\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"g\", {\n        fill: \"currentColor\",\n        mask: \"url(#strike)\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"none\"\n    }), glyph)));\n}\nconst CloseIcon = createIcon(\"Close\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n}));\nconst PreviousIcon = createIcon(\"Previous\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"\n}));\nconst NextIcon = createIcon(\"Next\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"\n}));\nconst LoadingIcon = createIcon(\"Loading\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, Array.from({\n    length: 8\n}).map((_, index, array)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        key: index,\n        x1: \"12\",\n        y1: \"6.5\",\n        x2: \"12\",\n        y2: \"1.8\",\n        strokeLinecap: \"round\",\n        strokeWidth: \"2.6\",\n        stroke: \"currentColor\",\n        strokeOpacity: 1 / array.length * (index + 1),\n        transform: `rotate(${360 / array.length * index}, 12, 12)`\n    }))));\nconst ErrorIcon = createIcon(\"Error\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z\"\n}));\nconst useLayoutEffect = hasWindow() ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMotionPreference() {\n    const [reduceMotion, setReduceMotion] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a, _b;\n        const mediaQuery = (_a = window.matchMedia) === null || _a === void 0 ? void 0 : _a.call(window, \"(prefers-reduced-motion: reduce)\");\n        setReduceMotion(mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.matches);\n        const listener = (event)=>setReduceMotion(event.matches);\n        (_b = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.addEventListener) === null || _b === void 0 ? void 0 : _b.call(mediaQuery, \"change\", listener);\n        return ()=>{\n            var _a;\n            return (_a = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.removeEventListener) === null || _a === void 0 ? void 0 : _a.call(mediaQuery, \"change\", listener);\n        };\n    }, []);\n    return reduceMotion;\n}\nfunction currentTransformation(node) {\n    let x = 0;\n    let y = 0;\n    let z = 0;\n    const matrix = window.getComputedStyle(node).transform;\n    const matcher = matrix.match(/matrix.*\\((.+)\\)/);\n    if (matcher) {\n        const values = matcher[1].split(\",\").map(parseInt);\n        if (values.length === 6) {\n            x = values[4];\n            y = values[5];\n        } else if (values.length === 16) {\n            x = values[12];\n            y = values[13];\n            z = values[14];\n        }\n    }\n    return {\n        x,\n        y,\n        z\n    };\n}\nfunction useAnimation(nodeRef, computeAnimation) {\n    const snapshot = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const animation = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const reduceMotion = useMotionPreference();\n    useLayoutEffect(()=>{\n        var _a, _b, _c;\n        if (nodeRef.current && snapshot.current !== undefined && !reduceMotion) {\n            const { keyframes, duration, easing, onfinish } = computeAnimation(snapshot.current, nodeRef.current.getBoundingClientRect(), currentTransformation(nodeRef.current)) || {};\n            if (keyframes && duration) {\n                (_a = animation.current) === null || _a === void 0 ? void 0 : _a.cancel();\n                animation.current = undefined;\n                try {\n                    animation.current = (_c = (_b = nodeRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, keyframes, {\n                        duration,\n                        easing\n                    });\n                } catch (err) {\n                    console.error(err);\n                }\n                if (animation.current) {\n                    animation.current.onfinish = ()=>{\n                        animation.current = undefined;\n                        onfinish === null || onfinish === void 0 ? void 0 : onfinish();\n                    };\n                }\n            }\n        }\n        snapshot.current = undefined;\n    });\n    return {\n        prepareAnimation: (currentSnapshot)=>{\n            snapshot.current = currentSnapshot;\n        },\n        isAnimationPlaying: ()=>{\n            var _a;\n            return ((_a = animation.current) === null || _a === void 0 ? void 0 : _a.playState) === \"running\";\n        }\n    };\n}\nfunction useContainerRect() {\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const observerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [containerRect, setContainerRect] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const setContainerRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        containerRef.current = node;\n        if (observerRef.current) {\n            observerRef.current.disconnect();\n            observerRef.current = undefined;\n        }\n        const updateContainerRect = ()=>{\n            if (node) {\n                const styles = window.getComputedStyle(node);\n                const parse = (value)=>parseFloat(value) || 0;\n                setContainerRect({\n                    width: Math.round(node.clientWidth - parse(styles.paddingLeft) - parse(styles.paddingRight)),\n                    height: Math.round(node.clientHeight - parse(styles.paddingTop) - parse(styles.paddingBottom))\n                });\n            } else {\n                setContainerRect(undefined);\n            }\n        };\n        updateContainerRect();\n        if (node && typeof ResizeObserver !== \"undefined\") {\n            observerRef.current = new ResizeObserver(updateContainerRect);\n            observerRef.current.observe(node);\n        }\n    }, []);\n    return {\n        setContainerRef,\n        containerRef,\n        containerRect\n    };\n}\nfunction useDelay() {\n    const timeoutId = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const { setTimeout, clearTimeout } = useTimeouts();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((callback, delay)=>{\n        clearTimeout(timeoutId.current);\n        timeoutId.current = setTimeout(callback, delay > 0 ? delay : 0);\n    }, [\n        setTimeout,\n        clearTimeout\n    ]);\n}\nfunction useEventCallback(fn) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n    useLayoutEffect(()=>{\n        ref.current = fn;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args)=>{\n        var _a;\n        return (_a = ref.current) === null || _a === void 0 ? void 0 : _a.call(ref, ...args);\n    }, []);\n}\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        ref(value);\n    } else if (ref) {\n        ref.current = value;\n    }\n}\nfunction useForkRef(refA, refB) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>refA == null && refB == null ? null : (refValue)=>{\n            setRef(refA, refValue);\n            setRef(refB, refValue);\n        }, [\n        refA,\n        refB\n    ]);\n}\nfunction useLoseFocus(focus, disabled = false) {\n    const focused = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    useLayoutEffect(()=>{\n        if (disabled && focused.current) {\n            focused.current = false;\n            focus();\n        }\n    }, [\n        disabled,\n        focus\n    ]);\n    const onFocus = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        focused.current = true;\n    }, []);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        focused.current = false;\n    }, []);\n    return {\n        onFocus,\n        onBlur\n    };\n}\nfunction useRTL() {\n    const [isRTL, setIsRTL] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useLayoutEffect(()=>{\n        setIsRTL(window.getComputedStyle(window.document.documentElement).direction === \"rtl\");\n    }, []);\n    return isRTL;\n}\nfunction useSensors() {\n    const [subscribers] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const notifySubscribers = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((type, event)=>{\n        var _a;\n        (_a = subscribers[type]) === null || _a === void 0 ? void 0 : _a.forEach((listener)=>{\n            if (!event.isPropagationStopped()) listener(event);\n        });\n    }, [\n        subscribers\n    ]);\n    const registerSensors = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            onPointerDown: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN, event),\n            onPointerMove: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE, event),\n            onPointerUp: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP, event),\n            onPointerLeave: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE, event),\n            onPointerCancel: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL, event),\n            onKeyDown: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, event),\n            onKeyUp: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_UP, event),\n            onWheel: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, event)\n        }), [\n        notifySubscribers\n    ]);\n    const subscribeSensors = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((type, callback)=>{\n        if (!subscribers[type]) {\n            subscribers[type] = [];\n        }\n        subscribers[type].unshift(callback);\n        return ()=>{\n            const listeners = subscribers[type];\n            if (listeners) {\n                listeners.splice(0, listeners.length, ...listeners.filter((el)=>el !== callback));\n            }\n        };\n    }, [\n        subscribers\n    ]);\n    return {\n        registerSensors,\n        subscribeSensors\n    };\n}\nfunction useThrottle(callback, delay) {\n    const lastCallbackTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const delayCallback = useDelay();\n    const executeCallback = useEventCallback((...args)=>{\n        lastCallbackTime.current = Date.now();\n        callback(args);\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args)=>{\n        delayCallback(()=>{\n            executeCallback(args);\n        }, delay - (Date.now() - lastCallbackTime.current));\n    }, [\n        delay,\n        executeCallback,\n        delayCallback\n    ]);\n}\nconst slidePrefix = makeComposePrefix(\"slide\");\nconst slideImagePrefix = makeComposePrefix(\"slide_image\");\nfunction ImageSlide({ slide: image, offset, render, rect, imageFit, imageProps, onClick, onLoad, onError, style }) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING);\n    const { publish } = useEvents();\n    const { setTimeout } = useTimeouts();\n    const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (offset === 0) {\n            publish((0,_types_js__WEBPACK_IMPORTED_MODULE_2__.activeSlideStatus)(status));\n        }\n    }, [\n        offset,\n        status,\n        publish\n    ]);\n    const handleLoading = useEventCallback((img)=>{\n        (\"decode\" in img ? img.decode() : Promise.resolve()).catch(()=>{}).then(()=>{\n            if (!img.parentNode) {\n                return;\n            }\n            setStatus(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE);\n            setTimeout(()=>{\n                onLoad === null || onLoad === void 0 ? void 0 : onLoad(img);\n            }, 0);\n        });\n    });\n    const setImageRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((img)=>{\n        imageRef.current = img;\n        if (img === null || img === void 0 ? void 0 : img.complete) {\n            handleLoading(img);\n        }\n    }, [\n        handleLoading\n    ]);\n    const handleOnLoad = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        handleLoading(event.currentTarget);\n    }, [\n        handleLoading\n    ]);\n    const handleOnError = useEventCallback(()=>{\n        setStatus(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR);\n        onError === null || onError === void 0 ? void 0 : onError();\n    });\n    const cover = isImageFitCover(image, imageFit);\n    const nonInfinite = (value, fallback)=>Number.isFinite(value) ? value : fallback;\n    const maxWidth = nonInfinite(Math.max(...((_b = (_a = image.srcSet) === null || _a === void 0 ? void 0 : _a.map((x)=>x.width)) !== null && _b !== void 0 ? _b : []).concat(image.width ? [\n        image.width\n    ] : []).filter(Boolean)), ((_c = imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalWidth) || 0);\n    const maxHeight = nonInfinite(Math.max(...((_e = (_d = image.srcSet) === null || _d === void 0 ? void 0 : _d.map((x)=>x.height)) !== null && _e !== void 0 ? _e : []).concat(image.height ? [\n        image.height\n    ] : []).filter(Boolean)), ((_f = imageRef.current) === null || _f === void 0 ? void 0 : _f.naturalHeight) || 0);\n    const defaultStyle = maxWidth && maxHeight ? {\n        maxWidth: `min(${maxWidth}px, 100%)`,\n        maxHeight: `min(${maxHeight}px, 100%)`\n    } : {\n        maxWidth: \"100%\",\n        maxHeight: \"100%\"\n    };\n    const srcSet = (_g = image.srcSet) === null || _g === void 0 ? void 0 : _g.sort((a, b)=>a.width - b.width).map((item)=>`${item.src} ${item.width}w`).join(\", \");\n    const estimateActualWidth = ()=>rect && !cover && image.width && image.height ? rect.height / image.height * image.width : Number.MAX_VALUE;\n    const sizes = srcSet && rect && hasWindow() ? `${Math.round(Math.min(estimateActualWidth(), rect.width))}px` : undefined;\n    const { style: imagePropsStyle, className: imagePropsClassName, ...restImageProps } = imageProps || {};\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"img\", {\n        ref: setImageRef,\n        onLoad: handleOnLoad,\n        onError: handleOnError,\n        onClick: onClick,\n        draggable: false,\n        className: clsx(cssClass(slideImagePrefix()), cover && cssClass(slideImagePrefix(\"cover\")), status !== _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE && cssClass(slideImagePrefix(\"loading\")), imagePropsClassName),\n        style: {\n            ...defaultStyle,\n            ...style,\n            ...imagePropsStyle\n        },\n        ...restImageProps,\n        alt: image.alt,\n        sizes: sizes,\n        srcSet: srcSet,\n        src: image.src\n    }), status !== _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLACEHOLDER))\n    }, status === _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING && ((render === null || render === void 0 ? void 0 : render.iconLoading) ? render.iconLoading() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LoadingIcon, {\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON), cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING)))\n    })), status === _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR && ((render === null || render === void 0 ? void 0 : render.iconError) ? render.iconError() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ErrorIcon, {\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON), cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR)))\n    }))));\n}\nconst LightboxRoot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function LightboxRoot({ className, children, ...rest }, ref) {\n    const nodeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DocumentContextProvider, {\n        nodeRef: nodeRef\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: useForkRef(ref, nodeRef),\n        className: clsx(cssClass(\"root\"), className),\n        ...rest\n    }, children));\n});\nvar SwipeState;\n(function(SwipeState) {\n    SwipeState[SwipeState[\"NONE\"] = 0] = \"NONE\";\n    SwipeState[SwipeState[\"SWIPE\"] = 1] = \"SWIPE\";\n    SwipeState[SwipeState[\"PULL\"] = 2] = \"PULL\";\n    SwipeState[SwipeState[\"ANIMATION\"] = 3] = \"ANIMATION\";\n})(SwipeState || (SwipeState = {}));\nfunction usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled) {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>!disabled ? cleanup(subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN, onPointerDown), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE, onPointerMove), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP, onPointerUp), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE, onPointerUp), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL, onPointerUp)) : ()=>{}, [\n        subscribeSensors,\n        onPointerDown,\n        onPointerMove,\n        onPointerUp,\n        disabled\n    ]);\n}\nvar Gesture;\n(function(Gesture) {\n    Gesture[Gesture[\"NONE\"] = 0] = \"NONE\";\n    Gesture[Gesture[\"SWIPE\"] = 1] = \"SWIPE\";\n    Gesture[Gesture[\"PULL\"] = 2] = \"PULL\";\n})(Gesture || (Gesture = {}));\nconst SWIPE_THRESHOLD = 30;\nfunction usePointerSwipe({ disableSwipeNavigation, closeOnBackdropClick }, subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel, pullUpEnabled, pullDownEnabled, onPullStart, onPullProgress, onPullFinish, onPullCancel, onClose) {\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointers = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const activePointer = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const startTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const gesture = react__WEBPACK_IMPORTED_MODULE_0__.useRef(Gesture.NONE);\n    const clearPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (activePointer.current === event.pointerId) {\n            activePointer.current = undefined;\n            gesture.current = Gesture.NONE;\n        }\n        const currentPointers = pointers.current;\n        currentPointers.splice(0, currentPointers.length, ...currentPointers.filter((p)=>p.pointerId !== event.pointerId));\n    }, []);\n    const addPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        clearPointer(event);\n        event.persist();\n        pointers.current.push(event);\n    }, [\n        clearPointer\n    ]);\n    const lookupPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>pointers.current.find(({ pointerId })=>event.pointerId === pointerId), []);\n    const onPointerDown = useEventCallback((event)=>{\n        addPointer(event);\n    });\n    const exceedsPullThreshold = (value, threshold)=>pullDownEnabled && value > threshold || pullUpEnabled && value < -threshold;\n    const onPointerUp = useEventCallback((event)=>{\n        const pointer = lookupPointer(event);\n        if (pointer) {\n            if (activePointer.current === event.pointerId) {\n                const duration = Date.now() - startTime.current;\n                const currentOffset = offset.current;\n                if (gesture.current === Gesture.SWIPE) {\n                    if (Math.abs(currentOffset) > 0.3 * containerWidth || Math.abs(currentOffset) > 5 && duration < swipeAnimationDuration) {\n                        onSwipeFinish(currentOffset, duration);\n                    } else {\n                        onSwipeCancel(currentOffset);\n                    }\n                } else if (gesture.current === Gesture.PULL) {\n                    if (exceedsPullThreshold(currentOffset, 2 * SWIPE_THRESHOLD)) {\n                        onPullFinish(currentOffset, duration);\n                    } else {\n                        onPullCancel(currentOffset);\n                    }\n                }\n                offset.current = 0;\n                gesture.current = Gesture.NONE;\n            } else {\n                const { target } = event;\n                if (closeOnBackdropClick && target instanceof HTMLElement && target === pointer.target && (target.classList.contains(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE)) || target.classList.contains(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER)))) {\n                    onClose();\n                }\n            }\n        }\n        clearPointer(event);\n    });\n    const onPointerMove = useEventCallback((event)=>{\n        const pointer = lookupPointer(event);\n        if (pointer) {\n            const isCurrentPointer = activePointer.current === event.pointerId;\n            if (event.buttons === 0) {\n                if (isCurrentPointer && offset.current !== 0) {\n                    onPointerUp(event);\n                } else {\n                    clearPointer(pointer);\n                }\n                return;\n            }\n            const deltaX = event.clientX - pointer.clientX;\n            const deltaY = event.clientY - pointer.clientY;\n            if (activePointer.current === undefined) {\n                const startGesture = (newGesture)=>{\n                    addPointer(event);\n                    activePointer.current = event.pointerId;\n                    startTime.current = Date.now();\n                    gesture.current = newGesture;\n                };\n                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > SWIPE_THRESHOLD && isSwipeValid(deltaX)) {\n                    if (!disableSwipeNavigation) {\n                        startGesture(Gesture.SWIPE);\n                        onSwipeStart();\n                    }\n                } else if (Math.abs(deltaY) > Math.abs(deltaX) && exceedsPullThreshold(deltaY, SWIPE_THRESHOLD)) {\n                    startGesture(Gesture.PULL);\n                    onPullStart();\n                }\n            } else if (isCurrentPointer) {\n                if (gesture.current === Gesture.SWIPE) {\n                    offset.current = deltaX;\n                    onSwipeProgress(deltaX);\n                } else if (gesture.current === Gesture.PULL) {\n                    offset.current = deltaY;\n                    onPullProgress(deltaY);\n                }\n            }\n        }\n    });\n    usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp);\n}\nfunction usePreventWheelDefaults({ preventDefaultWheelX, preventDefaultWheelY }) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const listener = useEventCallback((event)=>{\n        const horizontal = Math.abs(event.deltaX) > Math.abs(event.deltaY);\n        if (horizontal && preventDefaultWheelX || !horizontal && preventDefaultWheelY || event.ctrlKey) {\n            event.preventDefault();\n        }\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        var _a;\n        if (node) {\n            node.addEventListener(\"wheel\", listener, {\n                passive: false\n            });\n        } else {\n            (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(\"wheel\", listener);\n        }\n        ref.current = node;\n    }, [\n        listener\n    ]);\n}\nfunction useWheelSwipe(swipeState, subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel) {\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const intent = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const intentCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const resetCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const wheelInertia = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const wheelInertiaCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const startTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { setTimeout, clearTimeout } = useTimeouts();\n    const cancelSwipeIntentCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (intentCleanup.current) {\n            clearTimeout(intentCleanup.current);\n            intentCleanup.current = undefined;\n        }\n    }, [\n        clearTimeout\n    ]);\n    const cancelSwipeResetCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (resetCleanup.current) {\n            clearTimeout(resetCleanup.current);\n            resetCleanup.current = undefined;\n        }\n    }, [\n        clearTimeout\n    ]);\n    const handleCleanup = useEventCallback(()=>{\n        if (swipeState !== SwipeState.SWIPE) {\n            offset.current = 0;\n            startTime.current = 0;\n            cancelSwipeIntentCleanup();\n            cancelSwipeResetCleanup();\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleCleanup, [\n        swipeState,\n        handleCleanup\n    ]);\n    const handleCancelSwipe = useEventCallback((currentSwipeOffset)=>{\n        resetCleanup.current = undefined;\n        if (offset.current === currentSwipeOffset) {\n            onSwipeCancel(offset.current);\n        }\n    });\n    const onWheel = useEventCallback((event)=>{\n        if (event.ctrlKey) {\n            return;\n        }\n        if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {\n            return;\n        }\n        const setWheelInertia = (inertia)=>{\n            wheelInertia.current = inertia;\n            clearTimeout(wheelInertiaCleanup.current);\n            wheelInertiaCleanup.current = inertia > 0 ? setTimeout(()=>{\n                wheelInertia.current = 0;\n                wheelInertiaCleanup.current = undefined;\n            }, 300) : undefined;\n        };\n        if (swipeState === SwipeState.NONE) {\n            if (Math.abs(event.deltaX) <= 1.2 * Math.abs(wheelInertia.current)) {\n                setWheelInertia(event.deltaX);\n                return;\n            }\n            if (!isSwipeValid(-event.deltaX)) {\n                return;\n            }\n            intent.current += event.deltaX;\n            cancelSwipeIntentCleanup();\n            if (Math.abs(intent.current) > 30) {\n                intent.current = 0;\n                setWheelInertia(0);\n                startTime.current = Date.now();\n                onSwipeStart();\n            } else {\n                const currentSwipeIntent = intent.current;\n                intentCleanup.current = setTimeout(()=>{\n                    intentCleanup.current = undefined;\n                    if (currentSwipeIntent === intent.current) {\n                        intent.current = 0;\n                    }\n                }, swipeAnimationDuration);\n            }\n        } else if (swipeState === SwipeState.SWIPE) {\n            let newSwipeOffset = offset.current - event.deltaX;\n            newSwipeOffset = Math.min(Math.abs(newSwipeOffset), containerWidth) * Math.sign(newSwipeOffset);\n            offset.current = newSwipeOffset;\n            onSwipeProgress(newSwipeOffset);\n            cancelSwipeResetCleanup();\n            if (Math.abs(newSwipeOffset) > 0.2 * containerWidth) {\n                setWheelInertia(event.deltaX);\n                onSwipeFinish(newSwipeOffset, Date.now() - startTime.current);\n                return;\n            }\n            resetCleanup.current = setTimeout(()=>handleCancelSwipe(newSwipeOffset), 2 * swipeAnimationDuration);\n        } else {\n            setWheelInertia(event.deltaX);\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, onWheel), [\n        subscribeSensors,\n        onWheel\n    ]);\n}\nconst cssContainerPrefix = makeComposePrefix(\"container\");\nconst ControllerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useController = makeUseContext(\"useController\", \"ControllerContext\", ControllerContext);\nfunction Controller({ children, ...props }) {\n    var _a;\n    const { carousel, animation, controller, on, styles, render } = props;\n    const { closeOnPullUp, closeOnPullDown, preventDefaultWheelX, preventDefaultWheelY } = controller;\n    const [toolbarWidth, setToolbarWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const state = useLightboxState();\n    const dispatch = useLightboxDispatch();\n    const [swipeState, setSwipeState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(SwipeState.NONE);\n    const swipeOffset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pullOffset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pullOpacity = react__WEBPACK_IMPORTED_MODULE_0__.useRef(1);\n    const { registerSensors, subscribeSensors } = useSensors();\n    const { subscribe, publish } = useEvents();\n    const cleanupAnimationIncrement = useDelay();\n    const cleanupSwipeOffset = useDelay();\n    const cleanupPullOffset = useDelay();\n    const { containerRef, setContainerRef, containerRect } = useContainerRect();\n    const handleContainerRef = useForkRef(usePreventWheelDefaults({\n        preventDefaultWheelX,\n        preventDefaultWheelY\n    }), setContainerRef);\n    const carouselRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const setCarouselRef = useForkRef(carouselRef, undefined);\n    const { getOwnerDocument } = useDocumentContext();\n    const isRTL = useRTL();\n    const rtl = (value)=>(isRTL ? -1 : 1) * (typeof value === \"number\" ? value : 1);\n    const focus = useEventCallback(()=>{\n        var _a;\n        return (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    });\n    const getLightboxProps = useEventCallback(()=>props);\n    const getLightboxState = useEventCallback(()=>state);\n    const prev = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((params)=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV, params), [\n        publish\n    ]);\n    const next = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((params)=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT, params), [\n        publish\n    ]);\n    const close = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE), [\n        publish\n    ]);\n    const isSwipeValid = (offset)=>!(carousel.finite && (rtl(offset) > 0 && state.currentIndex === 0 || rtl(offset) < 0 && state.currentIndex === state.slides.length - 1));\n    const setSwipeOffset = (offset)=>{\n        var _a;\n        swipeOffset.current = offset;\n        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar(\"swipe_offset\"), `${Math.round(offset)}px`);\n    };\n    const setPullOffset = (offset)=>{\n        var _a, _b;\n        pullOffset.current = offset;\n        pullOpacity.current = (()=>{\n            const threshold = 60;\n            const minOpacity = 0.5;\n            const offsetValue = (()=>{\n                if (closeOnPullDown && offset > 0) return offset;\n                if (closeOnPullUp && offset < 0) return -offset;\n                return 0;\n            })();\n            return Math.min(Math.max(round(1 - offsetValue / threshold * (1 - minOpacity), 2), minOpacity), 1);\n        })();\n        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar(\"pull_offset\"), `${Math.round(offset)}px`);\n        (_b = containerRef.current) === null || _b === void 0 ? void 0 : _b.style.setProperty(cssVar(\"pull_opacity\"), `${pullOpacity.current}`);\n    };\n    const { prepareAnimation: preparePullAnimation } = useAnimation(carouselRef, (snapshot, rect, translate)=>{\n        if (carouselRef.current && containerRect) {\n            return {\n                keyframes: [\n                    {\n                        transform: `translate(0, ${snapshot.rect.y - rect.y + translate.y}px)`,\n                        opacity: snapshot.opacity\n                    },\n                    {\n                        transform: \"translate(0, 0)\",\n                        opacity: 1\n                    }\n                ],\n                duration: snapshot.duration,\n                easing: animation.easing.fade\n            };\n        }\n        return undefined;\n    });\n    const pull = (offset, cancel)=>{\n        if (closeOnPullUp || closeOnPullDown) {\n            setPullOffset(offset);\n            let duration = 0;\n            if (carouselRef.current) {\n                duration = animation.fade * (cancel ? 2 : 1);\n                preparePullAnimation({\n                    rect: carouselRef.current.getBoundingClientRect(),\n                    opacity: pullOpacity.current,\n                    duration\n                });\n            }\n            cleanupPullOffset(()=>{\n                setPullOffset(0);\n                setSwipeState(SwipeState.NONE);\n            }, duration);\n            setSwipeState(SwipeState.ANIMATION);\n            if (!cancel) {\n                close();\n            }\n        }\n    };\n    const { prepareAnimation, isAnimationPlaying } = useAnimation(carouselRef, (snapshot, rect, translate)=>{\n        var _a;\n        if (carouselRef.current && containerRect && ((_a = state.animation) === null || _a === void 0 ? void 0 : _a.duration)) {\n            const parsedSpacing = parseLengthPercentage(carousel.spacing);\n            const spacingValue = (parsedSpacing.percent ? parsedSpacing.percent * containerRect.width / 100 : parsedSpacing.pixel) || 0;\n            return {\n                keyframes: [\n                    {\n                        transform: `translate(${rtl(state.globalIndex - snapshot.index) * (containerRect.width + spacingValue) + snapshot.rect.x - rect.x + translate.x}px, 0)`\n                    },\n                    {\n                        transform: \"translate(0, 0)\"\n                    }\n                ],\n                duration: state.animation.duration,\n                easing: state.animation.easing\n            };\n        }\n        return undefined;\n    });\n    const swipe = useEventCallback((action)=>{\n        var _a, _b;\n        const currentSwipeOffset = action.offset || 0;\n        const swipeDuration = !currentSwipeOffset ? (_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe : animation.swipe;\n        const swipeEasing = !currentSwipeOffset && !isAnimationPlaying() ? animation.easing.navigation : animation.easing.swipe;\n        let { direction } = action;\n        const count = (_b = action.count) !== null && _b !== void 0 ? _b : 1;\n        let newSwipeState = SwipeState.ANIMATION;\n        let newSwipeAnimationDuration = swipeDuration * count;\n        if (!direction) {\n            const containerWidth = containerRect === null || containerRect === void 0 ? void 0 : containerRect.width;\n            const elapsedTime = action.duration || 0;\n            const expectedTime = containerWidth ? swipeDuration / containerWidth * Math.abs(currentSwipeOffset) : swipeDuration;\n            if (count !== 0) {\n                if (elapsedTime < expectedTime) {\n                    newSwipeAnimationDuration = newSwipeAnimationDuration / expectedTime * Math.max(elapsedTime, expectedTime / 5);\n                } else if (containerWidth) {\n                    newSwipeAnimationDuration = swipeDuration / containerWidth * (containerWidth - Math.abs(currentSwipeOffset));\n                }\n                direction = rtl(currentSwipeOffset) > 0 ? _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV : _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT;\n            } else {\n                newSwipeAnimationDuration = swipeDuration / 2;\n            }\n        }\n        let increment = 0;\n        if (direction === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV) {\n            if (isSwipeValid(rtl(1))) {\n                increment = -count;\n            } else {\n                newSwipeState = SwipeState.NONE;\n                newSwipeAnimationDuration = swipeDuration;\n            }\n        } else if (direction === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT) {\n            if (isSwipeValid(rtl(-1))) {\n                increment = count;\n            } else {\n                newSwipeState = SwipeState.NONE;\n                newSwipeAnimationDuration = swipeDuration;\n            }\n        }\n        newSwipeAnimationDuration = Math.round(newSwipeAnimationDuration);\n        cleanupSwipeOffset(()=>{\n            setSwipeOffset(0);\n            setSwipeState(SwipeState.NONE);\n        }, newSwipeAnimationDuration);\n        if (carouselRef.current) {\n            prepareAnimation({\n                rect: carouselRef.current.getBoundingClientRect(),\n                index: state.globalIndex\n            });\n        }\n        setSwipeState(newSwipeState);\n        publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE, {\n            type: \"swipe\",\n            increment,\n            duration: newSwipeAnimationDuration,\n            easing: swipeEasing\n        });\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a, _b;\n        if (((_a = state.animation) === null || _a === void 0 ? void 0 : _a.increment) && ((_b = state.animation) === null || _b === void 0 ? void 0 : _b.duration)) {\n            cleanupAnimationIncrement(()=>dispatch({\n                    type: \"swipe\",\n                    increment: 0\n                }), state.animation.duration);\n        }\n    }, [\n        state.animation,\n        dispatch,\n        cleanupAnimationIncrement\n    ]);\n    const swipeParams = [\n        subscribeSensors,\n        isSwipeValid,\n        (containerRect === null || containerRect === void 0 ? void 0 : containerRect.width) || 0,\n        animation.swipe,\n        ()=>setSwipeState(SwipeState.SWIPE),\n        (offset)=>setSwipeOffset(offset),\n        (offset, duration)=>swipe({\n                offset,\n                duration,\n                count: 1\n            }),\n        (offset)=>swipe({\n                offset,\n                count: 0\n            })\n    ];\n    const pullParams = [\n        ()=>{\n            if (closeOnPullDown) {\n                setSwipeState(SwipeState.PULL);\n            }\n        },\n        (offset)=>setPullOffset(offset),\n        (offset)=>pull(offset),\n        (offset)=>pull(offset, true)\n    ];\n    usePointerSwipe(controller, ...swipeParams, closeOnPullUp, closeOnPullDown, ...pullParams, close);\n    useWheelSwipe(swipeState, ...swipeParams);\n    const focusOnMount = useEventCallback(()=>{\n        if (controller.focus && getOwnerDocument().querySelector(`.${cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL)} .${cssClass(cssContainerPrefix())}`)) {\n            focus();\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(focusOnMount, [\n        focusOnMount\n    ]);\n    const onViewCallback = useEventCallback(()=>{\n        var _a;\n        (_a = on.view) === null || _a === void 0 ? void 0 : _a.call(on, {\n            index: state.currentIndex\n        });\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(onViewCallback, [\n        state.globalIndex,\n        onViewCallback\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>cleanup(subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV, (action)=>swipe({\n                direction: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV,\n                ...action\n            })), subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT, (action)=>swipe({\n                direction: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT,\n                ...action\n            })), subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE, (action)=>dispatch(action))), [\n        subscribe,\n        swipe,\n        dispatch\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            prev,\n            next,\n            close,\n            focus,\n            slideRect: containerRect ? computeSlideRect(containerRect, carousel.padding) : {\n                width: 0,\n                height: 0\n            },\n            containerRect: containerRect || {\n                width: 0,\n                height: 0\n            },\n            subscribeSensors,\n            containerRef,\n            setCarouselRef,\n            toolbarWidth,\n            setToolbarWidth\n        }), [\n        prev,\n        next,\n        close,\n        focus,\n        subscribeSensors,\n        containerRect,\n        containerRef,\n        setCarouselRef,\n        toolbarWidth,\n        setToolbarWidth,\n        carousel.padding\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(controller.ref, ()=>({\n            prev,\n            next,\n            close,\n            focus,\n            getLightboxProps,\n            getLightboxState\n        }), [\n        prev,\n        next,\n        close,\n        focus,\n        getLightboxProps,\n        getLightboxState\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: handleContainerRef,\n        className: clsx(cssClass(cssContainerPrefix()), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER)),\n        style: {\n            ...swipeState === SwipeState.SWIPE ? {\n                [cssVar(\"swipe_offset\")]: `${Math.round(swipeOffset.current)}px`\n            } : null,\n            ...swipeState === SwipeState.PULL ? {\n                [cssVar(\"pull_offset\")]: `${Math.round(pullOffset.current)}px`,\n                [cssVar(\"pull_opacity\")]: `${pullOpacity.current}`\n            } : null,\n            ...controller.touchAction !== \"none\" ? {\n                [cssVar(\"controller_touch_action\")]: controller.touchAction\n            } : null,\n            ...styles.container\n        },\n        ...controller.aria ? {\n            role: \"region\",\n            \"aria-live\": \"polite\",\n            \"aria-roledescription\": \"carousel\"\n        } : null,\n        tabIndex: -1,\n        ...registerSensors\n    }, containerRect && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ControllerContext.Provider, {\n        value: context\n    }, children, (_a = render.controls) === null || _a === void 0 ? void 0 : _a.call(render)));\n}\nconst ControllerModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER, Controller);\nfunction cssPrefix$2(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL, value);\n}\nfunction cssSlidePrefix(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE, value);\n}\nfunction CarouselSlide({ slide, offset }) {\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { currentIndex } = useLightboxState();\n    const { slideRect, focus } = useController();\n    const { render, carousel: { imageFit, imageProps }, on: { click: onClick }, styles: { slide: style } } = useLightboxProps();\n    const { getOwnerDocument } = useDocumentContext();\n    const offscreen = offset !== 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a;\n        if (offscreen && ((_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.contains(getOwnerDocument().activeElement))) {\n            focus();\n        }\n    }, [\n        offscreen,\n        focus,\n        getOwnerDocument\n    ]);\n    const renderSlide = ()=>{\n        var _a, _b, _c, _d;\n        let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, {\n            slide,\n            offset,\n            rect: slideRect\n        });\n        if (!rendered && isImageSlide(slide)) {\n            rendered = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ImageSlide, {\n                slide: slide,\n                offset: offset,\n                render: render,\n                rect: slideRect,\n                imageFit: imageFit,\n                imageProps: imageProps,\n                onClick: !offscreen ? ()=>onClick === null || onClick === void 0 ? void 0 : onClick({\n                        index: currentIndex\n                    }) : undefined\n            });\n        }\n        return rendered ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (_b = render.slideHeader) === null || _b === void 0 ? void 0 : _b.call(render, {\n            slide\n        }), ((_c = render.slideContainer) !== null && _c !== void 0 ? _c : ({ children })=>children)({\n            slide,\n            children: rendered\n        }), (_d = render.slideFooter) === null || _d === void 0 ? void 0 : _d.call(render, {\n            slide\n        })) : null;\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: containerRef,\n        className: clsx(cssClass(cssSlidePrefix()), !offscreen && cssClass(cssSlidePrefix(\"current\")), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER)),\n        ...makeInertWhen(offscreen),\n        style: style,\n        role: \"region\",\n        \"aria-roledescription\": \"slide\"\n    }, renderSlide());\n}\nfunction Placeholder() {\n    const style = useLightboxProps().styles.slide;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE),\n        style: style\n    });\n}\nfunction Carousel({ carousel }) {\n    const { slides, currentIndex, globalIndex } = useLightboxState();\n    const { setCarouselRef } = useController();\n    const spacingValue = parseLengthPercentage(carousel.spacing);\n    const paddingValue = parseLengthPercentage(carousel.padding);\n    const preload = calculatePreload(carousel, slides, 1);\n    const items = [];\n    if (hasSlides(slides)) {\n        for(let index = currentIndex - preload; index <= currentIndex + preload; index += 1){\n            const slide = getSlide(slides, index);\n            const key = globalIndex - currentIndex + index;\n            const placeholder = carousel.finite && (index < 0 || index > slides.length - 1);\n            items.push(!placeholder ? {\n                key: [\n                    `${key}`,\n                    getSlideKey(slide)\n                ].filter(Boolean).join(\"|\"),\n                offset: index - currentIndex,\n                slide\n            } : {\n                key\n            });\n        }\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: setCarouselRef,\n        className: clsx(cssClass(cssPrefix$2()), items.length > 0 && cssClass(cssPrefix$2(\"with_slides\"))),\n        style: {\n            [`${cssVar(cssPrefix$2(\"slides_count\"))}`]: items.length,\n            [`${cssVar(cssPrefix$2(\"spacing_px\"))}`]: spacingValue.pixel || 0,\n            [`${cssVar(cssPrefix$2(\"spacing_percent\"))}`]: spacingValue.percent || 0,\n            [`${cssVar(cssPrefix$2(\"padding_px\"))}`]: paddingValue.pixel || 0,\n            [`${cssVar(cssPrefix$2(\"padding_percent\"))}`]: paddingValue.percent || 0\n        }\n    }, items.map(({ key, slide, offset })=>slide ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CarouselSlide, {\n            key: key,\n            slide: slide,\n            offset: offset\n        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Placeholder, {\n            key: key\n        })));\n}\nconst CarouselModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL, Carousel);\nfunction useNavigationState() {\n    const { carousel } = useLightboxProps();\n    const { slides, currentIndex } = useLightboxState();\n    const prevDisabled = slides.length === 0 || carousel.finite && currentIndex === 0;\n    const nextDisabled = slides.length === 0 || carousel.finite && currentIndex === slides.length - 1;\n    return {\n        prevDisabled,\n        nextDisabled\n    };\n}\nfunction useKeyboardNavigation(subscribeSensors) {\n    var _a;\n    const isRTL = useRTL();\n    const { publish } = useEvents();\n    const { animation } = useLightboxProps();\n    const { prevDisabled, nextDisabled } = useNavigationState();\n    const throttle = ((_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe) / 2;\n    const prev = useThrottle(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV), throttle);\n    const next = useThrottle(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT), throttle);\n    const handleKeyDown = useEventCallback((event)=>{\n        switch(event.key){\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ESCAPE:\n                publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE);\n                break;\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_LEFT:\n                if (!(isRTL ? nextDisabled : prevDisabled)) (isRTL ? next : prev)();\n                break;\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_RIGHT:\n                if (!(isRTL ? prevDisabled : nextDisabled)) (isRTL ? prev : next)();\n                break;\n            default:\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, handleKeyDown), [\n        subscribeSensors,\n        handleKeyDown\n    ]);\n}\nfunction NavigationButton({ label, icon, renderIcon, action, onClick, disabled, style }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(IconButton, {\n        label: label,\n        icon: icon,\n        renderIcon: renderIcon,\n        className: cssClass(`navigation_${action}`),\n        disabled: disabled,\n        onClick: onClick,\n        style: style,\n        ...useLoseFocus(useController().focus, disabled)\n    });\n}\nfunction Navigation({ render: { buttonPrev, buttonNext, iconPrev, iconNext }, styles }) {\n    const { prev, next, subscribeSensors } = useController();\n    const { prevDisabled, nextDisabled } = useNavigationState();\n    useKeyboardNavigation(subscribeSensors);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, buttonPrev ? buttonPrev() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(NavigationButton, {\n        label: \"Previous\",\n        action: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV,\n        icon: PreviousIcon,\n        renderIcon: iconPrev,\n        style: styles.navigationPrev,\n        disabled: prevDisabled,\n        onClick: prev\n    }), buttonNext ? buttonNext() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(NavigationButton, {\n        label: \"Next\",\n        action: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT,\n        icon: NextIcon,\n        renderIcon: iconNext,\n        style: styles.navigationNext,\n        disabled: nextDisabled,\n        onClick: next\n    }));\n}\nconst NavigationModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NAVIGATION, Navigation);\nconst noScroll = cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL);\nconst noScrollPadding = cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING);\nfunction isHTMLElement(element) {\n    return \"style\" in element;\n}\nfunction padScrollbar(element, padding, rtl) {\n    const styles = window.getComputedStyle(element);\n    const property = rtl ? \"padding-left\" : \"padding-right\";\n    const computedValue = rtl ? styles.paddingLeft : styles.paddingRight;\n    const originalValue = element.style.getPropertyValue(property);\n    element.style.setProperty(property, `${(parseInt(computedValue) || 0) + padding}px`);\n    return ()=>{\n        if (originalValue) {\n            element.style.setProperty(property, originalValue);\n        } else {\n            element.style.removeProperty(property);\n        }\n    };\n}\nfunction NoScroll({ noScroll: { disabled }, children }) {\n    const rtl = useRTL();\n    const { getOwnerDocument, getOwnerWindow } = useDocumentContext();\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (disabled) return ()=>{};\n        const cleanup = [];\n        const ownerWindow = getOwnerWindow();\n        const { body, documentElement } = getOwnerDocument();\n        const scrollbar = Math.round(ownerWindow.innerWidth - documentElement.clientWidth);\n        if (scrollbar > 0) {\n            cleanup.push(padScrollbar(body, scrollbar, rtl));\n            const elements = body.getElementsByTagName(\"*\");\n            for(let i = 0; i < elements.length; i += 1){\n                const element = elements[i];\n                if (isHTMLElement(element) && ownerWindow.getComputedStyle(element).getPropertyValue(\"position\") === \"fixed\" && !element.classList.contains(noScrollPadding)) {\n                    cleanup.push(padScrollbar(element, scrollbar, rtl));\n                }\n            }\n        }\n        body.classList.add(noScroll);\n        return ()=>{\n            body.classList.remove(noScroll);\n            cleanup.forEach((clean)=>clean());\n        };\n    }, [\n        rtl,\n        disabled,\n        getOwnerDocument,\n        getOwnerWindow\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n}\nconst NoScrollModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NO_SCROLL, NoScroll);\nfunction cssPrefix$1(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL, value);\n}\nfunction setAttribute(element, attribute, value) {\n    const previousValue = element.getAttribute(attribute);\n    element.setAttribute(attribute, value);\n    return ()=>{\n        if (previousValue) {\n            element.setAttribute(attribute, previousValue);\n        } else {\n            element.removeAttribute(attribute);\n        }\n    };\n}\nfunction Portal({ children, animation, styles, className, on, portal, close }) {\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const cleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const restoreFocus = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { setTimeout } = useTimeouts();\n    const { subscribe } = useEvents();\n    const reduceMotion = useMotionPreference();\n    const animationDuration = !reduceMotion ? animation.fade : 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n        return ()=>{\n            setMounted(false);\n            setVisible(false);\n        };\n    }, []);\n    const handleCleanup = useEventCallback(()=>{\n        cleanup.current.forEach((clean)=>clean());\n        cleanup.current = [];\n    });\n    const handleClose = useEventCallback(()=>{\n        var _a;\n        setVisible(false);\n        handleCleanup();\n        (_a = on.exiting) === null || _a === void 0 ? void 0 : _a.call(on);\n        setTimeout(()=>{\n            var _a;\n            (_a = on.exited) === null || _a === void 0 ? void 0 : _a.call(on);\n            close();\n        }, animationDuration);\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE, handleClose), [\n        subscribe,\n        handleClose\n    ]);\n    const handleEnter = useEventCallback((node)=>{\n        var _a, _b, _c;\n        reflow(node);\n        setVisible(true);\n        (_a = on.entering) === null || _a === void 0 ? void 0 : _a.call(on);\n        const elements = (_c = (_b = node.parentNode) === null || _b === void 0 ? void 0 : _b.children) !== null && _c !== void 0 ? _c : [];\n        for(let i = 0; i < elements.length; i += 1){\n            const element = elements[i];\n            if ([\n                \"TEMPLATE\",\n                \"SCRIPT\",\n                \"STYLE\"\n            ].indexOf(element.tagName) === -1 && element !== node) {\n                cleanup.current.push(setAttribute(element, \"inert\", \"\"));\n                cleanup.current.push(setAttribute(element, \"aria-hidden\", \"true\"));\n            }\n        }\n        cleanup.current.push(()=>{\n            var _a, _b;\n            (_b = (_a = restoreFocus.current) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);\n        });\n        setTimeout(()=>{\n            var _a;\n            (_a = on.entered) === null || _a === void 0 ? void 0 : _a.call(on);\n        }, animationDuration);\n    });\n    const handleRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node) {\n            handleEnter(node);\n        } else {\n            handleCleanup();\n        }\n    }, [\n        handleEnter,\n        handleCleanup\n    ]);\n    return mounted ? /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxRoot, {\n        ref: handleRef,\n        className: clsx(className, cssClass(cssPrefix$1()), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING), visible && cssClass(cssPrefix$1(\"open\"))),\n        \"aria-modal\": true,\n        role: \"dialog\",\n        \"aria-live\": \"polite\",\n        \"aria-roledescription\": \"lightbox\",\n        style: {\n            ...animation.fade !== LightboxDefaultProps.animation.fade ? {\n                [cssVar(\"fade_animation_duration\")]: `${animationDuration}ms`\n            } : null,\n            ...animation.easing.fade !== LightboxDefaultProps.animation.easing.fade ? {\n                [cssVar(\"fade_animation_timing_function\")]: animation.easing.fade\n            } : null,\n            ...styles.root\n        },\n        onFocus: (event)=>{\n            if (!restoreFocus.current) {\n                restoreFocus.current = event.relatedTarget;\n            }\n        }\n    }, children), portal.root || document.body) : null;\n}\nconst PortalModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL, Portal);\nfunction Root({ children }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n}\nconst RootModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_ROOT, Root);\nfunction cssPrefix(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR, value);\n}\nfunction Toolbar({ toolbar: { buttons }, render: { buttonClose, iconClose }, styles }) {\n    const { close, setToolbarWidth } = useController();\n    const { setContainerRef, containerRect } = useContainerRect();\n    useLayoutEffect(()=>{\n        setToolbarWidth(containerRect === null || containerRect === void 0 ? void 0 : containerRect.width);\n    }, [\n        setToolbarWidth,\n        containerRect === null || containerRect === void 0 ? void 0 : containerRect.width\n    ]);\n    const renderCloseButton = ()=>{\n        if (buttonClose) return buttonClose();\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(IconButton, {\n            key: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE,\n            label: \"Close\",\n            icon: CloseIcon,\n            renderIcon: iconClose,\n            onClick: close\n        });\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: setContainerRef,\n        style: styles.toolbar,\n        className: cssClass(cssPrefix())\n    }, buttons === null || buttons === void 0 ? void 0 : buttons.map((button)=>button === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE ? renderCloseButton() : button));\n}\nconst ToolbarModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR, Toolbar);\nfunction renderNode(node, props) {\n    var _a;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(node.module.component, {\n        key: node.module.name,\n        ...props\n    }, (_a = node.children) === null || _a === void 0 ? void 0 : _a.map((child)=>renderNode(child, props)));\n}\nfunction mergeAnimation(defaultAnimation, animation = {}) {\n    const { easing: defaultAnimationEasing, ...restDefaultAnimation } = defaultAnimation;\n    const { easing, ...restAnimation } = animation;\n    return {\n        easing: {\n            ...defaultAnimationEasing,\n            ...easing\n        },\n        ...restDefaultAnimation,\n        ...restAnimation\n    };\n}\nfunction Lightbox({ carousel, animation, render, toolbar, controller, noScroll, on, plugins, slides, index, ...restProps }) {\n    const { animation: defaultAnimation, carousel: defaultCarousel, render: defaultRender, toolbar: defaultToolbar, controller: defaultController, noScroll: defaultNoScroll, on: defaultOn, slides: defaultSlides, index: defaultIndex, plugins: defaultPlugins, ...restDefaultProps } = LightboxDefaultProps;\n    const { config, augmentation } = withPlugins([\n        createNode(PortalModule, [\n            createNode(NoScrollModule, [\n                createNode(ControllerModule, [\n                    createNode(CarouselModule),\n                    createNode(ToolbarModule),\n                    createNode(NavigationModule)\n                ])\n            ])\n        ])\n    ], plugins || defaultPlugins);\n    const props = augmentation({\n        animation: mergeAnimation(defaultAnimation, animation),\n        carousel: {\n            ...defaultCarousel,\n            ...carousel\n        },\n        render: {\n            ...defaultRender,\n            ...render\n        },\n        toolbar: {\n            ...defaultToolbar,\n            ...toolbar\n        },\n        controller: {\n            ...defaultController,\n            ...controller\n        },\n        noScroll: {\n            ...defaultNoScroll,\n            ...noScroll\n        },\n        on: {\n            ...defaultOn,\n            ...on\n        },\n        ...restDefaultProps,\n        ...restProps\n    });\n    if (!props.open) return null;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxPropsProvider, {\n        ...props\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxStateProvider, {\n        slides: slides || defaultSlides,\n        index: parseInt(index || defaultIndex)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TimeoutsProvider, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(EventsProvider, null, renderNode(createNode(RootModule, config), props)))));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/captions/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/captions/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Captions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst cssPrefix = (className) => (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(`slide_${className}`);\n\nconst defaultCaptionsProps = {\n    descriptionTextAlign: \"start\",\n    descriptionMaxLines: 3,\n    showToggle: false,\n    hidden: false,\n};\nconst resolveCaptionsProps = (captions) => ({\n    ...defaultCaptionsProps,\n    ...captions,\n});\nfunction useCaptionsProps() {\n    const { captions } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    return resolveCaptionsProps(captions);\n}\n\nconst CaptionsContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useCaptions = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.makeUseContext)(\"useCaptions\", \"CaptionsContext\", CaptionsContext);\nfunction CaptionsContextProvider({ captions, children }) {\n    const { ref, hidden } = resolveCaptionsProps(captions);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!hidden);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        visible,\n        show: () => setVisible(true),\n        hide: () => setVisible(false),\n    }), [visible]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(ref, () => context, [context]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(CaptionsContext.Provider, { value: context }, children);\n}\n\nfunction Title({ title }) {\n    const { toolbarWidth } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { styles } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { visible } = useCaptions();\n    if (!visible)\n        return null;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.captionsTitleContainer, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)(cssPrefix(\"captions_container\"), cssPrefix(\"title_container\")) },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: cssPrefix(\"title\"), style: {\n                ...(toolbarWidth ? { [(0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssVar)(\"toolbar_width\")]: `${toolbarWidth}px` } : null),\n                ...styles.captionsTitle,\n            } }, title)));\n}\n\nfunction Description({ description }) {\n    const { descriptionTextAlign, descriptionMaxLines } = useCaptionsProps();\n    const { styles } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { visible } = useCaptions();\n    if (!visible)\n        return null;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.captionsDescriptionContainer, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)(cssPrefix(\"captions_container\"), cssPrefix(\"description_container\")) },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: cssPrefix(\"description\"), style: {\n                ...(descriptionTextAlign !== defaultCaptionsProps.descriptionTextAlign ||\n                    descriptionMaxLines !== defaultCaptionsProps.descriptionMaxLines\n                    ? {\n                        [(0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssVar)(\"slide_description_text_align\")]: descriptionTextAlign,\n                        [(0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssVar)(\"slide_description_max_lines\")]: descriptionMaxLines,\n                    }\n                    : null),\n                ...styles.captionsDescription,\n            } }, typeof description === \"string\"\n            ? description.split(\"\\n\").flatMap((line, index) => [...(index > 0 ? [react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"br\", { key: index })] : []), line])\n            : description)));\n}\n\nconst captionsIcon = () => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { strokeWidth: 2, stroke: \"currentColor\", strokeLinejoin: \"round\", fill: \"none\", d: \"M3 5l18 0l0 14l-18 0l0-14z\" }),\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M7 15h3c.55 0 1-.45 1-1v-1H9.5v.5h-2v-3h2v.5H11v-1c0-.55-.45-1-1-1H7c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1zm7 0h3c.55 0 1-.45 1-1v-1h-1.5v.5h-2v-3h2v.5H18v-1c0-.55-.45-1-1-1h-3c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1z\" })));\nconst CaptionsVisible = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"CaptionsVisible\", captionsIcon());\nconst CaptionsHidden = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIconDisabled)(\"CaptionsVisible\", captionsIcon());\nfunction CaptionsButton() {\n    const { visible, show, hide } = useCaptions();\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    if (render.buttonCaptions) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render.buttonCaptions({ visible, show, hide }));\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { label: visible ? \"Hide captions\" : \"Show captions\", icon: visible ? CaptionsVisible : CaptionsHidden, renderIcon: visible ? render.iconCaptionsVisible : render.iconCaptionsHidden, onClick: visible ? hide : show }));\n}\n\nfunction Captions({ augment, addModule }) {\n    augment(({ captions: captionsProps, render: { slideFooter: renderFooter, ...restRender }, toolbar, ...restProps }) => {\n        const captions = resolveCaptionsProps(captionsProps);\n        return {\n            render: {\n                slideFooter: ({ slide }) => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, renderFooter === null || renderFooter === void 0 ? void 0 :\n                    renderFooter({ slide }),\n                    slide.title && react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, { title: slide.title }),\n                    slide.description && react__WEBPACK_IMPORTED_MODULE_0__.createElement(Description, { description: slide.description }))),\n                ...restRender,\n            },\n            toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_CAPTIONS, captions.showToggle ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(CaptionsButton, null) : null),\n            captions,\n            ...restProps,\n        };\n    });\n    addModule((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createModule)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_CAPTIONS, CaptionsContextProvider));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/captions/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/download/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/download/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Download)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst defaultDownloadProps = {\n    download: undefined,\n};\nconst resolveDownloadProps = (download) => ({\n    ...defaultDownloadProps,\n    ...download,\n});\n\nfunction download(url, name) {\n    const xhr = new XMLHttpRequest();\n    xhr.open(\"GET\", url);\n    xhr.responseType = \"blob\";\n    xhr.onload = () => {\n        saveAs(xhr.response, name);\n    };\n    xhr.onerror = () => {\n        console.error(\"Failed to download file\");\n    };\n    xhr.send();\n}\nfunction corsEnabled(url) {\n    const xhr = new XMLHttpRequest();\n    xhr.open(\"HEAD\", url, false);\n    try {\n        xhr.send();\n    }\n    catch (_) {\n    }\n    return xhr.status >= 200 && xhr.status <= 299;\n}\nfunction click(link) {\n    try {\n        link.dispatchEvent(new MouseEvent(\"click\"));\n    }\n    catch (_) {\n        const event = document.createEvent(\"MouseEvents\");\n        event.initMouseEvent(\"click\", true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);\n        link.dispatchEvent(event);\n    }\n}\nfunction saveAs(source, name) {\n    const link = document.createElement(\"a\");\n    link.rel = \"noopener\";\n    link.download = name || \"\";\n    if (!link.download) {\n        link.target = \"_blank\";\n    }\n    if (typeof source === \"string\") {\n        link.href = source;\n        if (link.origin !== window.location.origin) {\n            if (corsEnabled(link.href)) {\n                download(source, name);\n            }\n            else {\n                link.target = \"_blank\";\n                click(link);\n            }\n        }\n        else {\n            click(link);\n        }\n    }\n    else {\n        link.href = URL.createObjectURL(source);\n        setTimeout(() => URL.revokeObjectURL(link.href), 30000);\n        setTimeout(() => click(link), 0);\n    }\n}\n\nconst DownloadIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"DownloadIcon\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M18 15v3H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zm-1-4-1.41-1.41L13 12.17V4h-2v8.17L8.41 9.59 7 11l5 5 5-5z\" }));\nfunction DownloadButton() {\n    const { render, on, download: downloadProps } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { download: customDownload } = resolveDownloadProps(downloadProps);\n    const { currentSlide, currentIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    if (render.buttonDownload) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render.buttonDownload());\n    }\n    const downloadUrl = (currentSlide &&\n        (currentSlide.downloadUrl ||\n            (typeof currentSlide.download === \"string\" && currentSlide.download) ||\n            (typeof currentSlide.download === \"object\" && currentSlide.download.url) ||\n            ((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(currentSlide) && currentSlide.src))) ||\n        undefined;\n    const canDownload = customDownload ? (currentSlide === null || currentSlide === void 0 ? void 0 : currentSlide.download) !== false : Boolean(downloadUrl);\n    const defaultDownload = () => {\n        if (currentSlide && downloadUrl) {\n            const downloadFilename = currentSlide.downloadFilename ||\n                (typeof currentSlide.download === \"object\" && currentSlide.download.filename) ||\n                undefined;\n            saveAs(downloadUrl, downloadFilename);\n        }\n    };\n    const handleDownload = () => {\n        var _a;\n        if (currentSlide) {\n            (customDownload || defaultDownload)({ slide: currentSlide, saveAs });\n            (_a = on.download) === null || _a === void 0 ? void 0 : _a.call(on, { index: currentIndex });\n        }\n    };\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { label: \"Download\", icon: DownloadIcon, renderIcon: render.iconDownload, disabled: !canDownload, onClick: handleDownload }));\n}\n\nfunction Download({ augment }) {\n    augment(({ toolbar, download, ...restProps }) => ({\n        toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_DOWNLOAD, react__WEBPACK_IMPORTED_MODULE_0__.createElement(DownloadButton, null)),\n        download: resolveDownloadProps(download),\n        ...restProps,\n    }));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/download/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/fullscreen/index.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/fullscreen/index.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Fullscreen)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst defaultFullscreenProps = {\n    auto: false,\n    ref: null,\n};\nconst resolveFullscreenProps = (fullscreen) => ({\n    ...defaultFullscreenProps,\n    ...fullscreen,\n});\n\nconst FullscreenContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useFullscreen = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.makeUseContext)(\"useFullscreen\", \"FullscreenContext\", FullscreenContext);\nfunction FullscreenContextProvider({ fullscreen: fullscreenProps, on, children }) {\n    const { auto, ref } = resolveFullscreenProps(fullscreenProps);\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [disabled, setDisabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const [fullscreen, setFullscreen] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const wasFullscreen = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const { getOwnerDocument } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useDocumentContext)();\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        var _a, _b, _c, _d;\n        const ownerDocument = getOwnerDocument();\n        setDisabled(!((_d = (_c = (_b = (_a = ownerDocument.fullscreenEnabled) !== null && _a !== void 0 ? _a : ownerDocument.webkitFullscreenEnabled) !== null && _b !== void 0 ? _b : ownerDocument.mozFullScreenEnabled) !== null && _c !== void 0 ? _c : ownerDocument.msFullscreenEnabled) !== null && _d !== void 0 ? _d : false));\n    }, [getOwnerDocument]);\n    const getFullscreenElement = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        var _a;\n        const ownerDocument = getOwnerDocument();\n        const fullscreenElement = ownerDocument.fullscreenElement ||\n            ownerDocument.webkitFullscreenElement ||\n            ownerDocument.mozFullScreenElement ||\n            ownerDocument.msFullscreenElement;\n        return ((_a = fullscreenElement === null || fullscreenElement === void 0 ? void 0 : fullscreenElement.shadowRoot) === null || _a === void 0 ? void 0 : _a.fullscreenElement) || fullscreenElement;\n    }, [getOwnerDocument]);\n    const enter = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        const container = containerRef.current;\n        try {\n            if (container.requestFullscreen) {\n                container.requestFullscreen().catch(() => { });\n            }\n            else if (container.webkitRequestFullscreen) {\n                container.webkitRequestFullscreen();\n            }\n            else if (container.mozRequestFullScreen) {\n                container.mozRequestFullScreen();\n            }\n            else if (container.msRequestFullscreen) {\n                container.msRequestFullscreen();\n            }\n        }\n        catch (_) {\n        }\n    }, []);\n    const exit = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        if (!getFullscreenElement())\n            return;\n        const ownerDocument = getOwnerDocument();\n        try {\n            if (ownerDocument.exitFullscreen) {\n                ownerDocument.exitFullscreen().catch(() => { });\n            }\n            else if (ownerDocument.webkitExitFullscreen) {\n                ownerDocument.webkitExitFullscreen();\n            }\n            else if (ownerDocument.mozCancelFullScreen) {\n                ownerDocument.mozCancelFullScreen();\n            }\n            else if (ownerDocument.msExitFullscreen) {\n                ownerDocument.msExitFullscreen();\n            }\n        }\n        catch (_) {\n        }\n    }, [getFullscreenElement, getOwnerDocument]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const ownerDocument = getOwnerDocument();\n        const listener = () => {\n            setFullscreen(getFullscreenElement() === containerRef.current);\n        };\n        return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cleanup)(...[\"fullscreenchange\", \"webkitfullscreenchange\", \"mozfullscreenchange\", \"MSFullscreenChange\"].map((event) => {\n            ownerDocument.addEventListener(event, listener);\n            return () => ownerDocument.removeEventListener(event, listener);\n        }));\n    }, [getFullscreenElement, getOwnerDocument]);\n    const onEnterFullscreen = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => { var _a; return (_a = on.enterFullscreen) === null || _a === void 0 ? void 0 : _a.call(on); });\n    const onExitFullscreen = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => { var _a; return (_a = on.exitFullscreen) === null || _a === void 0 ? void 0 : _a.call(on); });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (fullscreen) {\n            wasFullscreen.current = true;\n        }\n        if (wasFullscreen.current) {\n            (fullscreen ? onEnterFullscreen : onExitFullscreen)();\n        }\n    }, [fullscreen, onEnterFullscreen, onExitFullscreen]);\n    const handleAutoFullscreen = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a;\n        (_a = (auto ? enter : null)) === null || _a === void 0 ? void 0 : _a();\n        return exit;\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleAutoFullscreen, [handleAutoFullscreen]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ fullscreen, disabled, enter, exit }), [fullscreen, disabled, enter, exit]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(ref, () => context, [context]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: containerRef, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN), (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FULLSIZE)) },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullscreenContext.Provider, { value: context }, children)));\n}\n\nconst EnterFullscreenIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"EnterFullscreen\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z\" }));\nconst ExitFullscreenIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"ExitFullscreen\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z\" }));\nfunction FullscreenButton() {\n    var _a;\n    const { fullscreen, disabled, enter, exit } = useFullscreen();\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    if (disabled)\n        return null;\n    if (render.buttonFullscreen) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (_a = render.buttonFullscreen) === null || _a === void 0 ? void 0 : _a.call(render, { fullscreen, disabled, enter, exit }));\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { disabled: disabled, label: fullscreen ? \"Exit Fullscreen\" : \"Enter Fullscreen\", icon: fullscreen ? ExitFullscreenIcon : EnterFullscreenIcon, renderIcon: fullscreen ? render.iconExitFullscreen : render.iconEnterFullscreen, onClick: fullscreen ? exit : enter }));\n}\n\nfunction Fullscreen({ augment, contains, addParent }) {\n    augment(({ fullscreen, toolbar, ...restProps }) => ({\n        toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN, react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullscreenButton, null)),\n        fullscreen: resolveFullscreenProps(fullscreen),\n        ...restProps,\n    }));\n    addParent(contains(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_THUMBNAILS) ? _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_THUMBNAILS : _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER, (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createModule)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN, FullscreenContextProvider));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/fullscreen/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Zoom)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst defaultZoomProps = {\n    maxZoomPixelRatio: 1,\n    zoomInMultiplier: 2,\n    doubleTapDelay: 300,\n    doubleClickDelay: 500,\n    doubleClickMaxStops: 2,\n    keyboardMoveDistance: 50,\n    wheelZoomDistanceFactor: 100,\n    pinchZoomDistanceFactor: 100,\n    scrollToZoom: false,\n};\nconst resolveZoomProps = (zoom) => ({\n    ...defaultZoomProps,\n    ...zoom,\n});\n\nfunction useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef) {\n    const zoomAnimation = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const zoomAnimationStart = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const { zoom: zoomAnimationDuration } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)().animation;\n    const reduceMotion = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useMotionPreference)();\n    const playZoomAnimation = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a, _b, _c;\n        (_a = zoomAnimation.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        zoomAnimation.current = undefined;\n        if (zoomAnimationStart.current && (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)) {\n            try {\n                zoomAnimation.current = (_c = (_b = zoomWrapperRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, [\n                    { transform: zoomAnimationStart.current },\n                    { transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)` },\n                ], {\n                    duration: !reduceMotion ? (zoomAnimationDuration !== null && zoomAnimationDuration !== void 0 ? zoomAnimationDuration : 500) : 0,\n                    easing: zoomAnimation.current ? \"ease-out\" : \"ease-in-out\",\n                });\n            }\n            catch (err) {\n                console.error(err);\n            }\n            zoomAnimationStart.current = undefined;\n            if (zoomAnimation.current) {\n                zoomAnimation.current.onfinish = () => {\n                    zoomAnimation.current = undefined;\n                };\n            }\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(playZoomAnimation, [zoom, offsetX, offsetY, playZoomAnimation]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        zoomAnimationStart.current = (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)\n            ? window.getComputedStyle(zoomWrapperRef.current).transform\n            : undefined;\n    }, [zoomWrapperRef]);\n}\n\nfunction useZoomCallback(zoom, disabled) {\n    const { on } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const onZoomCallback = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a;\n        if (!disabled) {\n            (_a = on.zoom) === null || _a === void 0 ? void 0 : _a.call(on, { zoom });\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(onZoomCallback, [zoom, onZoomCallback]);\n}\n\nfunction useZoomProps() {\n    const { zoom } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    return resolveZoomProps(zoom);\n}\n\nfunction useZoomImageRect(slideRect, imageDimensions) {\n    var _a, _b;\n    let imageRect = { width: 0, height: 0 };\n    let maxImageRect = { width: 0, height: 0 };\n    const { currentSlide } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { imageFit } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)().carousel;\n    const { maxZoomPixelRatio } = useZoomProps();\n    if (slideRect && currentSlide) {\n        const slide = { ...currentSlide, ...imageDimensions };\n        if ((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(slide)) {\n            const cover = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageFitCover)(slide, imageFit);\n            const width = Math.max(...(((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.map((x) => x.width)) || []).concat(slide.width ? [slide.width] : []));\n            const height = Math.max(...(((_b = slide.srcSet) === null || _b === void 0 ? void 0 : _b.map((x) => x.height)) || []).concat(slide.height ? [slide.height] : []));\n            if (width > 0 && height > 0 && slideRect.width > 0 && slideRect.height > 0) {\n                maxImageRect = cover\n                    ? {\n                        width: Math.round(Math.min(width, (slideRect.width / slideRect.height) * height)),\n                        height: Math.round(Math.min(height, (slideRect.height / slideRect.width) * width)),\n                    }\n                    : { width, height };\n                maxImageRect = {\n                    width: maxImageRect.width * maxZoomPixelRatio,\n                    height: maxImageRect.height * maxZoomPixelRatio,\n                };\n                imageRect = cover\n                    ? {\n                        width: Math.min(slideRect.width, maxImageRect.width, width),\n                        height: Math.min(slideRect.height, maxImageRect.height, height),\n                    }\n                    : {\n                        width: Math.round(Math.min(slideRect.width, (slideRect.height / height) * width, width)),\n                        height: Math.round(Math.min(slideRect.height, (slideRect.width / width) * height, height)),\n                    };\n            }\n        }\n    }\n    const maxZoom = imageRect.width ? Math.max((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.round)(maxImageRect.width / imageRect.width, 5), 1) : 1;\n    return { imageRect, maxZoom };\n}\n\nfunction distance(pointerA, pointerB) {\n    return ((pointerA.clientX - pointerB.clientX) ** 2 + (pointerA.clientY - pointerB.clientY) ** 2) ** 0.5;\n}\nfunction scaleZoom(value, delta, factor = 100, clamp = 2) {\n    return value * Math.min(1 + Math.abs(delta / factor), clamp) ** Math.sign(delta);\n}\nfunction useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapperRef) {\n    const activePointers = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const lastPointerDown = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pinchZoomDistance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const { globalIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { getOwnerWindow } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useDocumentContext)();\n    const { containerRef, subscribeSensors } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { keyboardMoveDistance, zoomInMultiplier, wheelZoomDistanceFactor, scrollToZoom, doubleTapDelay, doubleClickDelay, doubleClickMaxStops, pinchZoomDistanceFactor, } = useZoomProps();\n    const translateCoordinates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        if (containerRef.current) {\n            const { pageX, pageY } = event;\n            const { scrollX, scrollY } = getOwnerWindow();\n            const { left, top, width, height } = containerRef.current.getBoundingClientRect();\n            return [pageX - left - scrollX - width / 2, pageY - top - scrollY - height / 2];\n        }\n        return [];\n    }, [containerRef, getOwnerWindow]);\n    const onKeyDown = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        const { key, metaKey, ctrlKey } = event;\n        const meta = metaKey || ctrlKey;\n        const preventDefault = () => {\n            event.preventDefault();\n            event.stopPropagation();\n        };\n        if (zoom > 1) {\n            const move = (deltaX, deltaY) => {\n                preventDefault();\n                changeOffsets(deltaX, deltaY);\n            };\n            if (key === \"ArrowDown\") {\n                move(0, keyboardMoveDistance);\n            }\n            else if (key === \"ArrowUp\") {\n                move(0, -keyboardMoveDistance);\n            }\n            else if (key === \"ArrowLeft\") {\n                move(-keyboardMoveDistance, 0);\n            }\n            else if (key === \"ArrowRight\") {\n                move(keyboardMoveDistance, 0);\n            }\n        }\n        const handleChangeZoom = (zoomValue) => {\n            preventDefault();\n            changeZoom(zoomValue);\n        };\n        if (key === \"+\" || (meta && key === \"=\")) {\n            handleChangeZoom(zoom * zoomInMultiplier);\n        }\n        else if (key === \"-\" || (meta && key === \"_\")) {\n            handleChangeZoom(zoom / zoomInMultiplier);\n        }\n        else if (meta && key === \"0\") {\n            handleChangeZoom(1);\n        }\n    });\n    const onWheel = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        if (event.ctrlKey || scrollToZoom) {\n            if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {\n                event.stopPropagation();\n                changeZoom(scaleZoom(zoom, -event.deltaY, wheelZoomDistanceFactor), true, ...translateCoordinates(event));\n                return;\n            }\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n            if (!scrollToZoom) {\n                changeOffsets(event.deltaX, event.deltaY);\n            }\n        }\n    });\n    const clearPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        const pointers = activePointers.current;\n        pointers.splice(0, pointers.length, ...pointers.filter((p) => p.pointerId !== event.pointerId));\n    }, []);\n    const replacePointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        clearPointer(event);\n        event.persist();\n        activePointers.current.push(event);\n    }, [clearPointer]);\n    const onPointerDown = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        var _a;\n        const pointers = activePointers.current;\n        if ((event.pointerType === \"mouse\" && event.buttons > 1) ||\n            !((_a = zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current) === null || _a === void 0 ? void 0 : _a.contains(event.target))) {\n            return;\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n        }\n        const { timeStamp } = event;\n        if (pointers.length === 0 &&\n            timeStamp - lastPointerDown.current < (event.pointerType === \"touch\" ? doubleTapDelay : doubleClickDelay)) {\n            lastPointerDown.current = 0;\n            changeZoom(zoom !== maxZoom ? zoom * Math.max(maxZoom ** (1 / doubleClickMaxStops), zoomInMultiplier) : 1, false, ...translateCoordinates(event));\n        }\n        else {\n            lastPointerDown.current = timeStamp;\n        }\n        replacePointer(event);\n        if (pointers.length === 2) {\n            pinchZoomDistance.current = distance(pointers[0], pointers[1]);\n        }\n    });\n    const onPointerMove = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        const pointers = activePointers.current;\n        const activePointer = pointers.find((p) => p.pointerId === event.pointerId);\n        if (pointers.length === 2 && pinchZoomDistance.current) {\n            event.stopPropagation();\n            replacePointer(event);\n            const currentDistance = distance(pointers[0], pointers[1]);\n            const delta = currentDistance - pinchZoomDistance.current;\n            if (Math.abs(delta) > 0) {\n                changeZoom(scaleZoom(zoom, delta, pinchZoomDistanceFactor), true, ...pointers\n                    .map((x) => translateCoordinates(x))\n                    .reduce((acc, coordinate) => coordinate.map((x, i) => acc[i] + x / 2)));\n                pinchZoomDistance.current = currentDistance;\n            }\n            return;\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n            if (activePointer) {\n                if (pointers.length === 1) {\n                    changeOffsets((activePointer.clientX - event.clientX) / zoom, (activePointer.clientY - event.clientY) / zoom);\n                }\n                replacePointer(event);\n            }\n        }\n    });\n    const onPointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        const pointers = activePointers.current;\n        if (pointers.length === 2 && pointers.find((p) => p.pointerId === event.pointerId)) {\n            pinchZoomDistance.current = undefined;\n        }\n        clearPointer(event);\n    }, [clearPointer]);\n    const cleanupSensors = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        const pointers = activePointers.current;\n        pointers.splice(0, pointers.length);\n        lastPointerDown.current = 0;\n        pinchZoomDistance.current = undefined;\n    }, []);\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.usePointerEvents)(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(cleanupSensors, [globalIndex, cleanupSensors]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (!disabled) {\n            return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cleanup)(cleanupSensors, subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, onKeyDown), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, onWheel));\n        }\n        return () => { };\n    }, [disabled, subscribeSensors, cleanupSensors, onKeyDown, onWheel]);\n}\n\nfunction useZoomState(imageRect, maxZoom, zoomWrapperRef) {\n    const [zoom, setZoom] = react__WEBPACK_IMPORTED_MODULE_0__.useState(1);\n    const [offsetX, setOffsetX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [offsetY, setOffsetY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const animate = useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef);\n    const { currentSlide, globalIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { containerRect, slideRect } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { zoomInMultiplier } = useZoomProps();\n    const currentSource = currentSlide && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(currentSlide) ? currentSlide.src : undefined;\n    const disabled = !currentSource || !(zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current);\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        setZoom(1);\n        setOffsetX(0);\n        setOffsetY(0);\n    }, [globalIndex, currentSource]);\n    const changeOffsets = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((dx, dy, targetZoom) => {\n        const newZoom = targetZoom || zoom;\n        const newOffsetX = offsetX - (dx || 0);\n        const newOffsetY = offsetY - (dy || 0);\n        const maxOffsetX = (imageRect.width * newZoom - slideRect.width) / 2 / newZoom;\n        const maxOffsetY = (imageRect.height * newZoom - slideRect.height) / 2 / newZoom;\n        setOffsetX(Math.min(Math.abs(newOffsetX), Math.max(maxOffsetX, 0)) * Math.sign(newOffsetX));\n        setOffsetY(Math.min(Math.abs(newOffsetY), Math.max(maxOffsetY, 0)) * Math.sign(newOffsetY));\n    }, [zoom, offsetX, offsetY, slideRect, imageRect.width, imageRect.height]);\n    const changeZoom = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((targetZoom, rapid, dx, dy) => {\n        const newZoom = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.round)(Math.min(Math.max(targetZoom + 0.001 < maxZoom ? targetZoom : maxZoom, 1), maxZoom), 5);\n        if (newZoom === zoom)\n            return;\n        if (!rapid) {\n            animate();\n        }\n        changeOffsets(dx ? dx * (1 / zoom - 1 / newZoom) : 0, dy ? dy * (1 / zoom - 1 / newZoom) : 0, newZoom);\n        setZoom(newZoom);\n    }, [zoom, maxZoom, changeOffsets, animate]);\n    const handleControllerRectChange = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        if (zoom > 1) {\n            if (zoom > maxZoom) {\n                changeZoom(maxZoom, true);\n            }\n            changeOffsets();\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(handleControllerRectChange, [containerRect.width, containerRect.height, handleControllerRectChange]);\n    const zoomIn = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => changeZoom(zoom * zoomInMultiplier), [zoom, zoomInMultiplier, changeZoom]);\n    const zoomOut = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => changeZoom(zoom / zoomInMultiplier), [zoom, zoomInMultiplier, changeZoom]);\n    return { zoom, offsetX, offsetY, disabled, changeOffsets, changeZoom, zoomIn, zoomOut };\n}\n\nconst ZoomControllerContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useZoom = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.makeUseContext)(\"useZoom\", \"ZoomControllerContext\", ZoomControllerContext);\nfunction ZoomContextProvider({ children }) {\n    const [zoomWrapper, setZoomWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const { slideRect } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { imageRect, maxZoom } = useZoomImageRect(slideRect, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.imageDimensions);\n    const { zoom, offsetX, offsetY, disabled, changeZoom, changeOffsets, zoomIn, zoomOut } = useZoomState(imageRect, maxZoom, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);\n    useZoomCallback(zoom, disabled);\n    useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);\n    const zoomRef = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ zoom, maxZoom, offsetX, offsetY, disabled, zoomIn, zoomOut, changeZoom }), [zoom, maxZoom, offsetX, offsetY, disabled, zoomIn, zoomOut, changeZoom]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(useZoomProps().ref, () => zoomRef, [zoomRef]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ ...zoomRef, setZoomWrapper }), [zoomRef, setZoomWrapper]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomControllerContext.Provider, { value: context }, children);\n}\n\nconst ZoomInIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"ZoomIn\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\" }),\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z\" })));\nconst ZoomOutIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"ZoomOut\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z\" }));\nconst ZoomButton = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function ZoomButton({ zoomIn, onLoseFocus }, ref) {\n    const wasEnabled = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const wasFocused = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const { zoom, maxZoom, zoomIn: zoomInCallback, zoomOut: zoomOutCallback, disabled: zoomDisabled } = useZoom();\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const disabled = zoomDisabled || (zoomIn ? zoom >= maxZoom : zoom <= 1);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (disabled && wasEnabled.current && wasFocused.current) {\n            onLoseFocus();\n        }\n        if (!disabled) {\n            wasEnabled.current = true;\n        }\n    }, [disabled, onLoseFocus]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { ref: ref, disabled: disabled, label: zoomIn ? \"Zoom in\" : \"Zoom out\", icon: zoomIn ? ZoomInIcon : ZoomOutIcon, renderIcon: zoomIn ? render.iconZoomIn : render.iconZoomOut, onClick: zoomIn ? zoomInCallback : zoomOutCallback, onFocus: () => {\n            wasFocused.current = true;\n        }, onBlur: () => {\n            wasFocused.current = false;\n        } }));\n});\n\nfunction ZoomButtonsGroup() {\n    const zoomInRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const zoomOutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { focus } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const focusSibling = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((sibling) => {\n        var _a, _b;\n        if (!((_a = sibling.current) === null || _a === void 0 ? void 0 : _a.disabled)) {\n            (_b = sibling.current) === null || _b === void 0 ? void 0 : _b.focus();\n        }\n        else {\n            focus();\n        }\n    }, [focus]);\n    const focusZoomIn = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => focusSibling(zoomInRef), [focusSibling]);\n    const focusZoomOut = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => focusSibling(zoomOutRef), [focusSibling]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButton, { zoomIn: true, ref: zoomInRef, onLoseFocus: focusZoomOut }),\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButton, { ref: zoomOutRef, onLoseFocus: focusZoomIn })));\n}\n\nfunction ZoomToolbarControl() {\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const zoomRef = useZoom();\n    if (render.buttonZoom) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render.buttonZoom(zoomRef));\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButtonsGroup, null);\n}\n\nfunction isResponsiveImageSlide(slide) {\n    var _a;\n    return (((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.length) || 0) > 0;\n}\nfunction reducer({ current, preload }, { type, source }) {\n    switch (type) {\n        case \"fetch\":\n            if (!current) {\n                return { current: source };\n            }\n            return { current, preload: source };\n        case \"done\":\n            if (source === preload) {\n                return { current: source };\n            }\n            return { current, preload };\n        default:\n            throw new Error(_types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE);\n    }\n}\nfunction ResponsiveImage(props) {\n    var _a, _b;\n    const [{ current, preload }, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(reducer, {});\n    const { slide: image, rect, imageFit, render, interactive } = props;\n    const srcSet = image.srcSet.sort((a, b) => a.width - b.width);\n    const width = (_a = image.width) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1].width;\n    const height = (_b = image.height) !== null && _b !== void 0 ? _b : srcSet[srcSet.length - 1].height;\n    const cover = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageFitCover)(image, imageFit);\n    const maxWidth = Math.max(...srcSet.map((x) => x.width));\n    const targetWidth = Math.min((cover ? Math.max : Math.min)(rect.width, width * (rect.height / height)), maxWidth);\n    const pixelDensity = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.devicePixelRatio)();\n    const handleResize = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a;\n        const targetSource = (_a = srcSet.find((x) => x.width >= targetWidth * pixelDensity)) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1];\n        if (!current || srcSet.findIndex((x) => x.src === current) < srcSet.findIndex((x) => x === targetSource)) {\n            dispatch({ type: \"fetch\", source: targetSource.src });\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(handleResize, [rect.width, rect.height, pixelDensity, handleResize]);\n    const handlePreload = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((currentPreload) => dispatch({ type: \"done\", source: currentPreload }));\n    const style = {\n        WebkitTransform: !interactive ? \"translateZ(0)\" : \"initial\",\n    };\n    if (!cover) {\n        Object.assign(style, rect.width / rect.height < width / height ? { width: \"100%\", height: \"auto\" } : { width: \"auto\", height: \"100%\" });\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n        preload && preload !== current && (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { key: \"preload\", ...props, offset: undefined, slide: { ...image, src: preload, srcSet: undefined }, style: { position: \"absolute\", visibility: \"hidden\", ...style }, onLoad: () => handlePreload(preload), render: {\n                ...render,\n                iconLoading: () => null,\n                iconError: () => null,\n            } })),\n        current && (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { key: \"current\", ...props, slide: { ...image, src: current, srcSet: undefined }, style: style }))));\n}\n\nfunction ZoomWrapper({ render, slide, offset, rect }) {\n    var _a;\n    const [imageDimensions, setImageDimensions] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const zoomWrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { zoom, maxZoom, offsetX, offsetY, setZoomWrapper } = useZoom();\n    const interactive = zoom > 1;\n    const { carousel, on } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { currentIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        if (offset === 0) {\n            setZoomWrapper({ zoomWrapperRef, imageDimensions });\n            return () => setZoomWrapper(undefined);\n        }\n        return () => { };\n    }, [offset, imageDimensions, setZoomWrapper]);\n    let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, { slide, offset, rect, zoom, maxZoom });\n    if (!rendered && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(slide)) {\n        const slideProps = {\n            slide,\n            offset,\n            rect,\n            render,\n            imageFit: carousel.imageFit,\n            imageProps: carousel.imageProps,\n            onClick: offset === 0 ? () => { var _a; return (_a = on.click) === null || _a === void 0 ? void 0 : _a.call(on, { index: currentIndex }); } : undefined,\n        };\n        rendered = isResponsiveImageSlide(slide) ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(ResponsiveImage, { ...slideProps, slide: slide, interactive: interactive, rect: offset === 0 ? { width: rect.width * zoom, height: rect.height * zoom } : rect })) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { onLoad: (img) => setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight }), ...slideProps }));\n    }\n    if (!rendered)\n        return null;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: zoomWrapperRef, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FULLSIZE), (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER), (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER), interactive && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER_INTERACTIVE)), style: offset === 0 ? { transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)` } : undefined }, rendered));\n}\n\nconst Zoom = ({ augment, addModule }) => {\n    augment(({ zoom: zoomProps, toolbar, render, controller, ...restProps }) => {\n        const zoom = resolveZoomProps(zoomProps);\n        return {\n            zoom,\n            toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM, react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomToolbarControl, null)),\n            render: {\n                ...render,\n                slide: (props) => { var _a; return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(props.slide) ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomWrapper, { render: render, ...props }) : (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, props); },\n            },\n            controller: { ...controller, preventDefaultWheelY: zoom.scrollToZoom },\n            ...restProps,\n        };\n    });\n    addModule((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createModule)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM, ZoomContextProvider));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/types.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/types.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_CLOSE: () => (/* binding */ ACTION_CLOSE),\n/* harmony export */   ACTION_NEXT: () => (/* binding */ ACTION_NEXT),\n/* harmony export */   ACTION_PREV: () => (/* binding */ ACTION_PREV),\n/* harmony export */   ACTION_SWIPE: () => (/* binding */ ACTION_SWIPE),\n/* harmony export */   ACTIVE_SLIDE_COMPLETE: () => (/* binding */ ACTIVE_SLIDE_COMPLETE),\n/* harmony export */   ACTIVE_SLIDE_ERROR: () => (/* binding */ ACTIVE_SLIDE_ERROR),\n/* harmony export */   ACTIVE_SLIDE_LOADING: () => (/* binding */ ACTIVE_SLIDE_LOADING),\n/* harmony export */   ACTIVE_SLIDE_PLAYING: () => (/* binding */ ACTIVE_SLIDE_PLAYING),\n/* harmony export */   CLASS_FLEX_CENTER: () => (/* binding */ CLASS_FLEX_CENTER),\n/* harmony export */   CLASS_FULLSIZE: () => (/* binding */ CLASS_FULLSIZE),\n/* harmony export */   CLASS_NO_SCROLL: () => (/* binding */ CLASS_NO_SCROLL),\n/* harmony export */   CLASS_NO_SCROLL_PADDING: () => (/* binding */ CLASS_NO_SCROLL_PADDING),\n/* harmony export */   CLASS_SLIDE: () => (/* binding */ CLASS_SLIDE),\n/* harmony export */   CLASS_SLIDE_WRAPPER: () => (/* binding */ CLASS_SLIDE_WRAPPER),\n/* harmony export */   CLASS_SLIDE_WRAPPER_INTERACTIVE: () => (/* binding */ CLASS_SLIDE_WRAPPER_INTERACTIVE),\n/* harmony export */   ELEMENT_BUTTON: () => (/* binding */ ELEMENT_BUTTON),\n/* harmony export */   ELEMENT_ICON: () => (/* binding */ ELEMENT_ICON),\n/* harmony export */   EVENT_ON_KEY_DOWN: () => (/* binding */ EVENT_ON_KEY_DOWN),\n/* harmony export */   EVENT_ON_KEY_UP: () => (/* binding */ EVENT_ON_KEY_UP),\n/* harmony export */   EVENT_ON_POINTER_CANCEL: () => (/* binding */ EVENT_ON_POINTER_CANCEL),\n/* harmony export */   EVENT_ON_POINTER_DOWN: () => (/* binding */ EVENT_ON_POINTER_DOWN),\n/* harmony export */   EVENT_ON_POINTER_LEAVE: () => (/* binding */ EVENT_ON_POINTER_LEAVE),\n/* harmony export */   EVENT_ON_POINTER_MOVE: () => (/* binding */ EVENT_ON_POINTER_MOVE),\n/* harmony export */   EVENT_ON_POINTER_UP: () => (/* binding */ EVENT_ON_POINTER_UP),\n/* harmony export */   EVENT_ON_WHEEL: () => (/* binding */ EVENT_ON_WHEEL),\n/* harmony export */   IMAGE_FIT_CONTAIN: () => (/* binding */ IMAGE_FIT_CONTAIN),\n/* harmony export */   IMAGE_FIT_COVER: () => (/* binding */ IMAGE_FIT_COVER),\n/* harmony export */   MODULE_CAROUSEL: () => (/* binding */ MODULE_CAROUSEL),\n/* harmony export */   MODULE_CONTROLLER: () => (/* binding */ MODULE_CONTROLLER),\n/* harmony export */   MODULE_NAVIGATION: () => (/* binding */ MODULE_NAVIGATION),\n/* harmony export */   MODULE_NO_SCROLL: () => (/* binding */ MODULE_NO_SCROLL),\n/* harmony export */   MODULE_PORTAL: () => (/* binding */ MODULE_PORTAL),\n/* harmony export */   MODULE_ROOT: () => (/* binding */ MODULE_ROOT),\n/* harmony export */   MODULE_TOOLBAR: () => (/* binding */ MODULE_TOOLBAR),\n/* harmony export */   PLUGIN_CAPTIONS: () => (/* binding */ PLUGIN_CAPTIONS),\n/* harmony export */   PLUGIN_COUNTER: () => (/* binding */ PLUGIN_COUNTER),\n/* harmony export */   PLUGIN_DOWNLOAD: () => (/* binding */ PLUGIN_DOWNLOAD),\n/* harmony export */   PLUGIN_FULLSCREEN: () => (/* binding */ PLUGIN_FULLSCREEN),\n/* harmony export */   PLUGIN_INLINE: () => (/* binding */ PLUGIN_INLINE),\n/* harmony export */   PLUGIN_SHARE: () => (/* binding */ PLUGIN_SHARE),\n/* harmony export */   PLUGIN_SLIDESHOW: () => (/* binding */ PLUGIN_SLIDESHOW),\n/* harmony export */   PLUGIN_THUMBNAILS: () => (/* binding */ PLUGIN_THUMBNAILS),\n/* harmony export */   PLUGIN_ZOOM: () => (/* binding */ PLUGIN_ZOOM),\n/* harmony export */   SLIDE_STATUS_COMPLETE: () => (/* binding */ SLIDE_STATUS_COMPLETE),\n/* harmony export */   SLIDE_STATUS_ERROR: () => (/* binding */ SLIDE_STATUS_ERROR),\n/* harmony export */   SLIDE_STATUS_LOADING: () => (/* binding */ SLIDE_STATUS_LOADING),\n/* harmony export */   SLIDE_STATUS_PLACEHOLDER: () => (/* binding */ SLIDE_STATUS_PLACEHOLDER),\n/* harmony export */   SLIDE_STATUS_PLAYING: () => (/* binding */ SLIDE_STATUS_PLAYING),\n/* harmony export */   UNKNOWN_ACTION_TYPE: () => (/* binding */ UNKNOWN_ACTION_TYPE),\n/* harmony export */   VK_ARROW_LEFT: () => (/* binding */ VK_ARROW_LEFT),\n/* harmony export */   VK_ARROW_RIGHT: () => (/* binding */ VK_ARROW_RIGHT),\n/* harmony export */   VK_ESCAPE: () => (/* binding */ VK_ESCAPE),\n/* harmony export */   activeSlideStatus: () => (/* binding */ activeSlideStatus)\n/* harmony export */ });\nconst MODULE_CAROUSEL = \"carousel\";\nconst MODULE_CONTROLLER = \"controller\";\nconst MODULE_NAVIGATION = \"navigation\";\nconst MODULE_NO_SCROLL = \"no-scroll\";\nconst MODULE_PORTAL = \"portal\";\nconst MODULE_ROOT = \"root\";\nconst MODULE_TOOLBAR = \"toolbar\";\nconst PLUGIN_CAPTIONS = \"captions\";\nconst PLUGIN_COUNTER = \"counter\";\nconst PLUGIN_DOWNLOAD = \"download\";\nconst PLUGIN_FULLSCREEN = \"fullscreen\";\nconst PLUGIN_INLINE = \"inline\";\nconst PLUGIN_SHARE = \"share\";\nconst PLUGIN_SLIDESHOW = \"slideshow\";\nconst PLUGIN_THUMBNAILS = \"thumbnails\";\nconst PLUGIN_ZOOM = \"zoom\";\nconst SLIDE_STATUS_LOADING = \"loading\";\nconst SLIDE_STATUS_PLAYING = \"playing\";\nconst SLIDE_STATUS_ERROR = \"error\";\nconst SLIDE_STATUS_COMPLETE = \"complete\";\nconst SLIDE_STATUS_PLACEHOLDER = \"placeholder\";\nconst activeSlideStatus = (status) => `active-slide-${status}`;\nconst ACTIVE_SLIDE_LOADING = activeSlideStatus(SLIDE_STATUS_LOADING);\nconst ACTIVE_SLIDE_PLAYING = activeSlideStatus(SLIDE_STATUS_PLAYING);\nconst ACTIVE_SLIDE_ERROR = activeSlideStatus(SLIDE_STATUS_ERROR);\nconst ACTIVE_SLIDE_COMPLETE = activeSlideStatus(SLIDE_STATUS_COMPLETE);\nconst CLASS_FULLSIZE = \"fullsize\";\nconst CLASS_FLEX_CENTER = \"flex_center\";\nconst CLASS_NO_SCROLL = \"no_scroll\";\nconst CLASS_NO_SCROLL_PADDING = \"no_scroll_padding\";\nconst CLASS_SLIDE = \"slide\";\nconst CLASS_SLIDE_WRAPPER = \"slide_wrapper\";\nconst CLASS_SLIDE_WRAPPER_INTERACTIVE = \"slide_wrapper_interactive\";\nconst ACTION_PREV = \"prev\";\nconst ACTION_NEXT = \"next\";\nconst ACTION_SWIPE = \"swipe\";\nconst ACTION_CLOSE = \"close\";\nconst EVENT_ON_POINTER_DOWN = \"onPointerDown\";\nconst EVENT_ON_POINTER_MOVE = \"onPointerMove\";\nconst EVENT_ON_POINTER_UP = \"onPointerUp\";\nconst EVENT_ON_POINTER_LEAVE = \"onPointerLeave\";\nconst EVENT_ON_POINTER_CANCEL = \"onPointerCancel\";\nconst EVENT_ON_KEY_DOWN = \"onKeyDown\";\nconst EVENT_ON_KEY_UP = \"onKeyUp\";\nconst EVENT_ON_WHEEL = \"onWheel\";\nconst VK_ESCAPE = \"Escape\";\nconst VK_ARROW_LEFT = \"ArrowLeft\";\nconst VK_ARROW_RIGHT = \"ArrowRight\";\nconst ELEMENT_BUTTON = \"button\";\nconst ELEMENT_ICON = \"icon\";\nconst IMAGE_FIT_CONTAIN = \"contain\";\nconst IMAGE_FIT_COVER = \"cover\";\nconst UNKNOWN_ACTION_TYPE = \"Unknown action type\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_ff86b5249bc2f62c58b39f122a04c227/node_modules/yet-another-react-lightbox/dist/types.js\n");

/***/ })

};
;