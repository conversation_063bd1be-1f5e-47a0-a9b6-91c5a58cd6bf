'use client'

import React from 'react'
import Link from 'next/link'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipTrigger } from '@/components/ui'
import VesselIcon from '@/app/ui/vessels/vesel-icon'
import { LocationModal } from '@/app/ui/vessels/list'
import { cn } from '@/app/lib/utils'

interface VesselLocationDisplayProps {
    /** Vessel object with id, title, and other vessel properties */
    vessel?: any
    /** Vessel ID for link generation (falls back to vessel.id) */
    vesselId?: number | string
    /** Display text (falls back to vessel.title) */
    displayText?: string
    /** Fallback text when no vessel or display text is available */
    fallbackText?: string
    /** Whether to show the vessel link (default: false) */
    showLink?: boolean
    /** Whether to show the location modal (default: false) */
    showLocationModal?: boolean
    /** Custom className for the container */
    className?: string
    /** Custom className for the location modal */
    locationModalClassName?: string
    /** Custom className for the location modal icon */
    locationModalIconClassName?: string
    /** Custom className for the link */
    linkClassName?: string
    /** Responsive breakpoint for hiding the link (e.g., 'laptop:block') */
    linkBreakpoint?: string
    /** Whether to make tooltip mobile clickable */
    mobileClickable?: boolean
}

export const VesselLocationDisplay: React.FC<VesselLocationDisplayProps> = ({
    vessel,
    vesselId,
    displayText,
    fallbackText = '',
    showLink = false,
    showLocationModal = false,
    className = '',
    locationModalClassName = '',
    locationModalIconClassName = 'size-6',
    linkClassName = 'hover:text-curious-blue-400',
    linkBreakpoint = '',
    mobileClickable = false,
}) => {
    // Determine the actual vessel ID to use
    const actualVesselId = vesselId || vessel?.id
    
    // Determine the display text
    const actualDisplayText = displayText || vessel?.title || fallbackText
    
    // Don't render if no vessel and no display text
    if (!vessel && !displayText) {
        return null
    }

    return (
        <div className={cn('flex relative items-center gap-2', className)}>
            {/* Vessel Icon with Tooltip */}
            {vessel && (
                <Tooltip mobileClickable={mobileClickable}>
                    <TooltipTrigger mobileClickable={mobileClickable}>
                        <div className="size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8">
                            <VesselIcon vessel={vessel} />
                        </div>
                    </TooltipTrigger>
                    <TooltipContent>
                        {vessel?.title || 'Vessel'}
                    </TooltipContent>
                </Tooltip>
            )}

            {/* Optional Link */}
            {showLink && actualVesselId && actualDisplayText && (
                <Link
                    href={`/vessel/info?id=${actualVesselId}`}
                    className={cn(
                        linkClassName,
                        linkBreakpoint,
                        'text-nowrap'
                    )}
                >
                    {actualDisplayText}
                </Link>
            )}

            {/* Optional Location Modal */}
            {showLocationModal && vessel && (
                <LocationModal
                    vessel={vessel}
                    className={locationModalClassName}
                    iconClassName={locationModalIconClassName}
                />
            )}
        </div>
    )
}

export default VesselLocationDisplay
