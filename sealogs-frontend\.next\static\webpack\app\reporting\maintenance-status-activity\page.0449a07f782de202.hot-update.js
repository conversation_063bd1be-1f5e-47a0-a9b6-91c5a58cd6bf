"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/maintenance-status-activity/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../reporting/maintenance-status-activity-report */ \"(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\");\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 171,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 178,\n        columnNumber: 9\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData)();\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_basicComponent, _maintenanceCheck_assignedTo;\n                const maintenanceCheck = row.original;\n                var _maintenanceCheck_name;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                                children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_4___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 25\n                        }, this),\n                        !showVessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \" hover:text-curious-blue-400\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 29\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden space-y-2\",\n                            children: [\n                                ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-outer-space-400\",\n                                            children: [\n                                                \"Assigned to:\",\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                            className: \"hover:text-curious-blue-400\",\n                                            children: maintenanceCheck.assignedTo.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                                        maintenanceCheck: maintenanceCheck\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        ...showVessel ? [] : [\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 27\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:block\",\n                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                            vessel: vesselDetails,\n                            vesselId: maintenanceCheck.basicComponent.id,\n                            displayText: maintenanceCheck.basicComponent.title || \"\",\n                            showLink: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 39\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 31\n                    }, this);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ],\n        {\n            accessorKey: \"assigned\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                const maintenanceCheck = row.original;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 37\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                className: \"hover:text-curious-blue-400 hidden lg:block\",\n                                children: maintenanceCheck.assignedTo.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventory\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                var _maintenanceCheck_inventory;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:text-curious-blue-400\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const maintenanceCheck = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 402,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"Zp7KzlBBuyYf0TXkH/rj/yT9nuA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_19__.useBreakpoints)();\n    const [filterIsOverDue, setFilterIsOverDue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (permissions) {\n            init_permissions();\n        }\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_21__.ReadComponentMaintenanceCheckList, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].nodes;\n            if (data) {\n                if (filterIsOverDue) {\n                    const isOverDue = data.filter((task)=>{\n                        return task.isOverDue.day < 0;\n                    });\n                    handleSetMaintenanceChecks(isOverDue);\n                } else {\n                    handleSetMaintenanceChecks(data);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let filter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0,\n                archived: searchParams.get(\"archived\") === \"true\" ? 1 : 0,\n                filter: filter\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_21__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_21__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        // let filteredTasks = maintenanceChecks || []\n        setFilterIsOverDue(false);\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                if (data.value !== \"Overdue\") {\n                    searchFilter.status = {\n                        eq: data.value\n                    };\n                } else {\n                    delete searchFilter.status;\n                    setFilterIsOverDue(true);\n                }\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+(typeof item === \"object\" ? item.value : item))\n                };\n            } else if (data && !Array.isArray(data)) {\n                // Handle both object format {value: id} and direct ID value\n                const memberId = typeof data === \"object\" ? data.value : data;\n                searchFilter.assignedToID = {\n                    eq: +memberId\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(data.startDate).format(\"YYYY-MM-DD 00:00:00\"),\n                    lte: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(data.endDate).format(\"YYYY-MM-DD 23:59:59\")\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        // let recurringFilter = null\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                searchFilter.taskType = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.taskType;\n            }\n        }\n        // // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\") {\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_6___default()(data === null || data === void 0 ? void 0 : data.value))) {\n                searchFilter.name = {\n                    contains: data === null || data === void 0 ? void 0 : data.value\n                };\n                keyFilter = data === null || data === void 0 ? void 0 : data.value;\n            } else {\n                delete searchFilter.name;\n                keyFilter = null;\n            }\n        } else if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(keyFilter)) {\n            searchFilter.name = {\n                contains: keyFilter !== null && keyFilter !== void 0 ? keyFilter : \"\"\n            };\n        } else {\n            delete searchFilter.name;\n            keyFilter = null;\n        }\n        // if (type === 'keyword' || (keyFilter && keyFilter.length > 0)) {\n        //     const keyword = data?.value?.trim().toLowerCase()\n        //     if (keyword && keyword.length > 0) {\n        //         filteredTasks = filteredTasks.filter(\n        //             (maintenanceCheck: MaintenanceCheck) =>\n        //                 [\n        //                     maintenanceCheck.name,\n        //                     maintenanceCheck.comments,\n        //                     maintenanceCheck.workOrderNumber,\n        //                 ].some((field) =>\n        //                     field?.toLowerCase().includes(keyword),\n        //                 ),\n        //         )\n        //         keyFilter = data.value\n        //     } else {\n        //         keyFilter = null\n        //     }\n        // }\n        // Filtering based on current searchFilter\n        // // Filter by vessel (basicComponentID)\n        // if (searchFilter.basicComponentID) {\n        //     const ids = searchFilter.basicComponentID.in || [\n        //         searchFilter.basicComponentID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.basicComponent?.id),\n        //     )\n        // }\n        // Filter by status\n        // if (searchFilter.status) {\n        //     const statuses = searchFilter.status.in || [searchFilter.status.eq]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         statuses.includes(mc.status),\n        //     )\n        // }\n        // // Filter by assignedToID\n        // if (searchFilter.assignedToID) {\n        //     const ids = searchFilter.assignedToID.in || [\n        //         searchFilter.assignedToID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.assignedTo?.id),\n        //     )\n        // }\n        // Filter by category\n        // if (searchFilter.maintenanceCategoryID) {\n        //     const ids = searchFilter.maintenanceCategoryID.in || [\n        //         searchFilter.maintenanceCategoryID.eq,\n        //     ]\n        //     filteredTasks = filteredTasks.filter((mc: MaintenanceCheck) =>\n        //         ids.includes(mc.maintenanceCategoryID),\n        //     )\n        // }\n        // // Filter by date range\n        // if (\n        //     searchFilter.expires &&\n        //     searchFilter.expires.gte &&\n        //     searchFilter.expires.lte\n        // ) {\n        //     filteredTasks = filteredTasks.filter(\n        //         (mc: MaintenanceCheck) =>\n        //             dayjs(mc.startDate).isAfter(\n        //                 dayjs(searchFilter.expires!.gte),\n        //             ) &&\n        //             dayjs(mc.startDate).isBefore(\n        //                 dayjs(searchFilter.expires!.lte),\n        //             ),\n        //     )\n        // }\n        // Filter by recurring status\n        // if (recurringFilter) {\n        //     if (recurringFilter === 'recurring') {\n        //         // Recurring tasks have recurringID > 0\n        //         filteredTasks = filteredTasks.filter(\n        //             (mc: MaintenanceCheck) => mc.recurringID > 0,\n        //         )\n        //     } else if (recurringFilter === 'one-off') {\n        //         // One-off tasks have recurringID = 0 or null\n        //         filteredTasks = filteredTasks.filter(\n        //             (mc: MaintenanceCheck) =>\n        //                 !mc.recurringID || mc.recurringID === 0,\n        //         )\n        //     }\n        // }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        // setFilteredMaintenanceChecks(filteredTasks)\n        loadMaintenanceChecks(searchFilter);\n    };\n    const downloadCsv = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task\",\n                \"location\",\n                \"assigned to\",\n                \"due\"\n            ]\n        ];\n        maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").forEach((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            csvEntries.push([\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_10__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (!maintenanceChecks || !vessels) {\n            return;\n        }\n        const headers = [\n            [\n                \"Task Name\",\n                \"Location\",\n                \"Assigned To\",\n                \"Due\"\n            ]\n        ];\n        const body = maintenanceChecks.filter((maintenanceCheck)=>maintenanceCheck.status !== \"Save_As_Draft\").map((maintenanceCheck)=>{\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n            const assignedToName = crewDetails ? \"\".concat(crewDetails.firstName, \" \").concat(crewDetails.surname) : ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.name) || \"\";\n            return [\n                maintenanceCheck.name,\n                (vessels === null || vessels === void 0 ? void 0 : vessels.filter((vessel)=>{\n                    var _maintenanceCheck_basicComponent;\n                    return (vessel === null || vessel === void 0 ? void 0 : vessel.id) == ((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id);\n                }).map((vessel)=>vessel.title).join(\", \")) || \"\",\n                assignedToName,\n                (0,_reporting_maintenance_status_activity_report__WEBPACK_IMPORTED_MODULE_9__.dueStatusLabel)(maintenanceCheck.isOverDue)\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_11__.exportPdfTable)({\n            headers,\n            body\n        });\n    };\n    const getStatusColorClasses = (status)=>{\n        switch(status){\n            case \"High\":\n                return \"text-destructive hover:text-cinnabar-800\";\n            case \"Upcoming\":\n                return \"text-warning hover:text-fire-bush-500\";\n            default:\n                return \"hover:text-curious-blue-400\";\n        }\n    };\n    const createMaintenanceColumns = (crewInfo, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.createColumns)([\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Title\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 869,\n                        columnNumber: 21\n                    }, this);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_basicComponent, _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_assignedTo1, _maintenanceCheck_inventory, _maintenanceCheck_basicComponent1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    var _maintenanceCheck_name;\n                    const taskLink = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString()),\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_23__.cn)(\"leading-tight truncate\", getStatusColorClasses(overDueStatus)),\n                        children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_4___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 886,\n                        columnNumber: 25\n                    }, this);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"tablet-sm:hidden inline-flex overflow-auto items-center gap-1.5\",\n                                children: [\n                                    ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                            className: \"text-xs\",\n                                            children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid\",\n                                        children: [\n                                            taskLink,\n                                            ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                    className: \"hover:text-curious-blue-400\",\n                                                    children: maintenanceCheck.inventory.item\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 45\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden tablet-sm:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid items-center gap-2\",\n                                        children: taskLink\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                            className: \"hover:text-curious-blue-400\",\n                                            children: maintenanceCheck.basicComponent.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 938,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"location\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 967,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1, _maintenanceCheck_basicComponent2;\n                    const maintenanceCheck = row.original;\n                    const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, vessels);\n                    // Get vessel with icon data\n                    const vesselWithIcon = getVesselWithIcon(((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) || 0, vesselDetails);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                            vessel: vesselWithIcon,\n                            vesselId: maintenanceCheck.basicComponent.id,\n                            displayText: maintenanceCheck.basicComponent.title || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 987,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_basicComponent, _rowA_original, _rowB_original_basicComponent, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_basicComponent = _rowA_original.basicComponent) === null || _rowA_original_basicComponent === void 0 ? void 0 : _rowA_original_basicComponent.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_basicComponent = _rowB_original.basicComponent) === null || _rowB_original_basicComponent === void 0 ? void 0 : _rowB_original_basicComponent.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"assigned\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Assigned\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1013,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-lg\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n                    const maintenanceCheck = row.original;\n                    const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, crewInfo);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_16__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1028,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                    className: \"hover:text-curious-blue-400 hidden tablet-md:block\",\n                                    children: maintenanceCheck.assignedTo.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1027,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_assignedTo, _rowA_original, _rowB_original_assignedTo, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_assignedTo = _rowA_original.assignedTo) === null || _rowA_original_assignedTo === void 0 ? void 0 : _rowA_original_assignedTo.name) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_assignedTo = _rowB_original.assignedTo) === null || _rowB_original_assignedTo === void 0 ? void 0 : _rowB_original_assignedTo.name) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            {\n                accessorKey: \"inventory\",\n                header: \"Inventory\",\n                cellAlignment: \"left\",\n                breakpoint: \"tablet-sm\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_inventory;\n                    const maintenanceCheck = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                            className: \"hover:text-curious-blue-400\",\n                            children: maintenanceCheck.inventory.item\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1065,\n                            columnNumber: 33\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1071,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                }\n            },\n            {\n                accessorKey: \"status\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_13__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 1080,\n                        columnNumber: 21\n                    }, this);\n                },\n                cellAlignment: \"right\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                    const maintenanceCheck = row.original;\n                    if (!maintenanceCheck) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1087,\n                            columnNumber: 32\n                        }, this);\n                    }\n                    const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                    const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" items-end w-fit text-sm xs:text-base py-0.5 px-1 xs:px-3 xs:py-1\\n                                                    \".concat(overDueStatus === \"High\" ? \"alert whitespace-nowrap\" : \"\", \"\\n                                                    \"),\n                            children: overDueDays * -1 + \" days ago\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1097,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1104,\n                            columnNumber: 37\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                            maintenanceCheck: maintenanceCheck\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 1109,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_isOverDue, _rowA_original, _rowB_original_isOverDue, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_isOverDue = _rowA_original.isOverDue) === null || _rowA_original_isOverDue === void 0 ? void 0 : _rowA_original_isOverDue.days) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_isOverDue = _rowB_original.isOverDue) === null || _rowB_original_isOverDue === void 0 ? void 0 : _rowB_original_isOverDue.days) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ]);\n    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon);\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_15__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_20__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1162,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_14__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1164,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1159,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_12__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1169,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_2__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1177,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1167,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"nONp+74DL7ZhxuCYhXSkcUYEX0s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_19__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_24__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});