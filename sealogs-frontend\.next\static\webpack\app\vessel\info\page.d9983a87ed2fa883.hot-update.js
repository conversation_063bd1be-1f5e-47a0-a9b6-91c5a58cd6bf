"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/components/ui/vessel-location-display.tsx":
/*!*******************************************************!*\
  !*** ./src/components/ui/vessel-location-display.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VesselLocationDisplay: function() { return /* binding */ VesselLocationDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_ui_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/ui/vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_ui_vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/ui/vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* __next_internal_client_entry_do_not_use__ VesselLocationDisplay,default auto */ \n\n\n\n\n\nconst VesselLocationDisplay = (param)=>{\n    let { vessel, vesselId, displayText, fallbackText = \"\", showLocationModal = false, mobileClickable = false } = param;\n    // Determine the actual vessel ID to use\n    const actualVesselId = vesselId || (vessel === null || vessel === void 0 ? void 0 : vessel.id);\n    // Determine the display text\n    const actualDisplayText = displayText || (vessel === null || vessel === void 0 ? void 0 : vessel.title) || fallbackText;\n    // Don't render if no vessel and no display text\n    if (!vessel && !displayText) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex relative items-center gap-2.5\",\n        children: [\n            vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                mobileClickable: mobileClickable,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                        mobileClickable: mobileClickable,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                vessel: vessel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                        children: (vessel === null || vessel === void 0 ? void 0 : vessel.title) || \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                lineNumber: 47,\n                columnNumber: 17\n            }, undefined),\n            actualDisplayText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: showLink && actualVesselId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: \"/vessel/info?id=\".concat(actualVesselId),\n                    className: \"hover:text-curious-blue-400 hidden laptop:block text-nowrap\",\n                    children: actualDisplayText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hover:text-curious-blue-400 text-nowrap\",\n                    children: actualDisplayText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false),\n            showLocationModal && vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                vessel: vessel,\n                className: \"absolute laptop:static -top-1.5 -right-2.5\",\n                iconClassName: \"size-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                lineNumber: 76,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n        lineNumber: 44,\n        columnNumber: 9\n    }, undefined);\n};\n_c = VesselLocationDisplay;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VesselLocationDisplay);\nvar _c;\n$RefreshReg$(_c, \"VesselLocationDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\n"));

/***/ })

});