"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/layout",{

/***/ "(app-pages-browser)/./src/components/ui/vessel-location-display.tsx":
/*!*******************************************************!*\
  !*** ./src/components/ui/vessel-location-display.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VesselLocationDisplay: function() { return /* binding */ VesselLocationDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_ui_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/ui/vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_ui_vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/ui/vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ VesselLocationDisplay,default auto */ \n\n\n\n\n\n\nconst VesselLocationDisplay = (param)=>{\n    let { vessel, vesselId, displayText, fallbackText = \"\", showLink = false, showLocationModal = false, className = \"\", locationModalClassName = \"\", locationModalIconClassName = \"size-6\", linkClassName = \"hover:text-curious-blue-400\", linkBreakpoint = \"\", mobileClickable = false } = param;\n    // Determine the actual vessel ID to use\n    const actualVesselId = vesselId || (vessel === null || vessel === void 0 ? void 0 : vessel.id);\n    // Determine the display text\n    const actualDisplayText = displayText || (vessel === null || vessel === void 0 ? void 0 : vessel.title) || fallbackText;\n    // Don't render if no vessel and no display text\n    if (!vessel && !displayText) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"flex relative items-center gap-2\", className),\n        children: [\n            vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                mobileClickable: mobileClickable,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                        mobileClickable: mobileClickable,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                vessel: vessel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                        children: (vessel === null || vessel === void 0 ? void 0 : vessel.title) || \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                lineNumber: 56,\n                columnNumber: 17\n            }, undefined),\n            showLink && actualVesselId && actualDisplayText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/vessel/info?id=\".concat(actualVesselId),\n                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(linkClassName, linkBreakpoint, \"text-nowrap\"),\n                children: actualDisplayText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                lineNumber: 68,\n                columnNumber: 17\n            }, undefined),\n            showLocationModal && vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                vessel: vessel,\n                className: locationModalClassName,\n                iconClassName: locationModalIconClassName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                lineNumber: 81,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n        lineNumber: 53,\n        columnNumber: 9\n    }, undefined);\n};\n_c = VesselLocationDisplay;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VesselLocationDisplay);\nvar _c;\n$RefreshReg$(_c, \"VesselLocationDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\n"));

/***/ })

});