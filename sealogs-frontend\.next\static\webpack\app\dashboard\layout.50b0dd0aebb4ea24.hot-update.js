"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/ui/vessel-location-display.tsx":
/*!*******************************************************!*\
  !*** ./src/components/ui/vessel-location-display.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VesselLocationDisplay: function() { return /* binding */ VesselLocationDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_ui_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/ui/vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_ui_vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/ui/vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* __next_internal_client_entry_do_not_use__ VesselLocationDisplay,default auto */ \n\n\n\n\n\nconst VesselLocationDisplay = (param)=>{\n    let { vessel, vesselId, displayText, fallbackText = \"\", showLink = false, showLocationModal = false, mobileClickable = false } = param;\n    // Determine the actual vessel ID to use\n    const actualVesselId = vesselId || (vessel === null || vessel === void 0 ? void 0 : vessel.id);\n    // Determine the display text\n    const actualDisplayText = displayText || (vessel === null || vessel === void 0 ? void 0 : vessel.title) || fallbackText;\n    // Don't render if no vessel and no display text\n    if (!vessel && !displayText) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex relative items-center gap-2.5\",\n        children: [\n            vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                mobileClickable: mobileClickable,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                        mobileClickable: mobileClickable,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                vessel: vessel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                        children: (vessel === null || vessel === void 0 ? void 0 : vessel.title) || \"Vessel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                lineNumber: 51,\n                columnNumber: 17\n            }, undefined),\n            showLink && actualVesselId && actualDisplayText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/vessel/info?id=\".concat(actualVesselId),\n                className: \"hover:text-curious-blue-400 hidden laptop:block text-nowrap\",\n                children: actualDisplayText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                lineNumber: 63,\n                columnNumber: 17\n            }, undefined),\n            showLocationModal && vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                vessel: vessel,\n                className: locationModalClassName,\n                iconClassName: locationModalIconClassName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n                lineNumber: 72,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\vessel-location-display.tsx\",\n        lineNumber: 48,\n        columnNumber: 9\n    }, undefined);\n};\n_c = VesselLocationDisplay;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VesselLocationDisplay);\nvar _c;\n$RefreshReg$(_c, \"VesselLocationDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\n"));

/***/ })

});